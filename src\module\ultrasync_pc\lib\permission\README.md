# 权限管理器系统

这是一个完整的权限管理器系统，用于管理Vue2多页面项目中的用户权限控制。

## 系统架构

```
PermissionManager (主权限管理器单例)
├── RoutePermissionManager (路由权限管理器)
├── ComponentPermissionManager (组件权限管理器)
└── FeaturePermissionManager (功能权限管理器)
```

## 快速开始

### 1. 安装和初始化

```javascript
// 在main.js或入口文件中
import permissionManager from './lib/permission';

// 初始化权限管理器
const userInfo = {
    uid: '123',
    role: 2,
    type: 1,
    nickname: '用户名'
};

permissionManager.initialize(userInfo);
```

### 2. 作为Vue插件使用

```javascript
import Vue from 'vue';
import PermissionPlugin from './lib/permission';

Vue.use(PermissionPlugin, {
    autoInit: true,
    userInfo: {
        uid: '123',
        role: 2,
        type: 1
    }
});
```

## 使用方法

### 1. 路由权限控制

```javascript
// 在路由守卫中使用
router.beforeEach((to, from, next) => {
    if (permissionManager.checkRoutePermission(to.path)) {
        next();
    } else {
        const redirectRoute = permissionManager.getRedirectRoute(to.path);
        next(redirectRoute);
    }
});

// 在组件中使用
if (this.$checkRoute('/admin/users')) {
    // 有权限访问用户管理页面
}
```

### 2. 组件权限控制

```javascript
// 在Vue组件中
export default {
    computed: {
        // 检查菜单项是否可见
        isAdminMenuVisible() {
            return this.$checkComponent('menu', 'background_manage');
        },

        // 检查按钮是否禁用
        isDeleteButtonDisabled() {
            return !this.$checkComponent('button', 'delete');
        }
    }
}
```

### 3. 功能权限控制

```javascript
// 检查用户管理权限
if (permissionManager.checkFeaturePermission('user_management', 'create_user')) {
    // 可以创建用户
}

// 检查API权限
if (permissionManager.checkApiPermission('POST', '/api/user/create')) {
    // 可以调用创建用户API
}

// 检查数据权限
if (permissionManager.checkDataPermission('user_data', 'all_data')) {
    // 可以访问所有用户数据
}
```

### 4. 使用Vue指令

```vue
<template>
    <!-- 基础用法：没有权限时移除元素 -->
    <button v-permission="'user_create'">创建用户</button>

    <!-- 隐藏元素而不是移除 -->
    <button v-permission.hide="'user_delete'">删除用户</button>

    <!-- 禁用元素 -->
    <button v-permission.disable="'user_edit'">编辑用户</button>

    <!-- 指定权限类型 -->
    <div v-permission.route="'/admin'">管理员面板</div>
    <div v-permission.component="'menu.admin_panel'">管理菜单</div>
    <div v-permission.feature="'user_management'">用户管理</div>

    <!-- 复杂权限检查 -->
    <button v-permission="{
        type: 'feature',
        permission: 'user_management',
        action: 'create_user',
        context: { departmentId: 123 }
    }">创建部门用户</button>
</template>
```

### 5. 批量权限检查

```javascript
// 批量检查多个权限
const permissions = permissionManager.batchCheckPermissions({
    routes: [
        { route: '/admin', key: 'admin_access' },
        { route: '/users', key: 'user_access' }
    ],
    components: [
        { component: 'menu', action: 'background_manage', key: 'admin_menu' },
        { component: 'button', action: 'delete', key: 'delete_btn' }
    ],
    features: [
        { feature: 'user_management', action: 'create_user', key: 'create_user' },
        { feature: 'group_management', action: 'edit_group', key: 'edit_group' }
    ]
});

console.log(permissions);
// {
//     admin_access: true,
//     user_access: true,
//     admin_menu: false,
//     delete_btn: false,
//     create_user: true,
//     edit_group: true
// }
```

## 角色权限配置

系统预定义了以下角色：

- `1`: 普通用户
- `2`: 管理员
- `3`: 高级管理员
- `4`: 仲裁者
- `5`: 超级管理员
- `6`: 采购者

## API参考

### 主要方法

- `initialize(userInfo, config)` - 初始化权限管理器
- `checkRoutePermission(routePath, context)` - 检查路由权限
- `checkComponentPermission(component, action, context)` - 检查组件权限
- `checkFeaturePermission(feature, action, context)` - 检查功能权限
- `checkApiPermission(method, path, context)` - 检查API权限
- `checkDataPermission(dataType, scope, context)` - 检查数据权限
- `isComponentVisible(component, action, context)` - 获取组件可见性
- `isComponentDisabled(component, action, context)` - 获取组件禁用状态
- `updateUserInfo(userInfo)` - 更新用户信息
- `clearCache()` - 清除权限缓存
- `isAdmin()` - 检查是否为管理员
- `isSuperAdmin()` - 检查是否为超级管理员

### Vue混入方法

- `$checkRoute(routePath, context)` - 检查路由权限
- `$checkComponent(component, action, context)` - 检查组件权限
- `$checkFeature(feature, action, context)` - 检查功能权限
- `$isAdmin()` - 检查是否为管理员
- `$isSuperAdmin()` - 检查是否为超级管理员
- `$getUserRole()` - 获取用户角色

## 扩展配置

### 添加自定义权限配置

```javascript
// 添加路由权限
permissionManager.getManager('route').addRoutePermission('/custom-route', {
    roles: [2, 3, 5],
    permissions: ['custom_permission']
});

// 添加组件权限
permissionManager.getManager('component').addComponentPermission('custom-component', {
    'custom_action': { roles: [2, 3], permissions: ['custom_action'] }
});

// 添加功能权限
permissionManager.getManager('feature').addFeaturePermission('custom-feature', {
    'custom_operation': { roles: [3, 5], permissions: ['custom_operation'] }
});
```

## 事件系统

权限管理器会触发以下事件：

- `permission:initialized` - 初始化完成
- `permission:userInfoUpdated` - 用户信息更新
- `permission:destroyed` - 管理器销毁

```javascript
// 监听事件
window.addEventListener('permission:initialized', (event) => {
    console.log('权限管理器初始化完成', event.detail);
});

// 在Vue组件中监听
this.$on('permission:userInfoUpdated', (userInfo) => {
    console.log('用户信息已更新', userInfo);
});
```

## 注意事项

1. 权限管理器必须在使用前调用 `initialize()` 方法进行初始化
2. 用户信息变更时需要调用 `updateUserInfo()` 方法更新权限
3. 权限配置支持角色和特定权限两种检查方式
4. 建议在路由守卫中集成路由权限检查
5. 组件权限检查建议在计算属性中使用，以便响应式更新
