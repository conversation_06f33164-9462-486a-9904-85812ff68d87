<template>
    <div class="init-container">
        <loading-page v-if="loading && !timeout" />
        <div v-else-if="timeout" class="timeout-ui">
            <div class="timeout-text">Loading failed, please try again later</div>
            <button class="refresh-btn" @click="refreshPage">refresh</button>
        </div>

        <!-- 隐私协议弹窗 -->
        <PrivacyPolicy
            :show.sync="showPrivacyPolicy"
            @handleAfterAgreePrivacyPolicy="handlePrivacyPolicyAgreed"
        />
    </div>
</template>

<script>
import CWorkstationCommunicationMng from "@/common/CommunicationMng/index";
import Tool from "@/common/tool.js";
import service from "../service/service";
import languages from "@/common/language";
import LoadingPage from "../components/loadingPage.vue";
import PrivacyPolicy from "../components/privacyPolicy.vue";
import { checkPrivacyPolicy } from "../lib/common_base.js";
import { designatedClientType,clientTypeMap } from '../../../../config/clientType'
import { setLanguage } from "@/common/i18n";
export default {
    name: "InitPage",
    components: {
        LoadingPage,
        PrivacyPolicy,
    },
    data() {
        return {
            globalParams: this.$store.state.globalParams,
            loading: true,
            timeout: false,
            timeoutTimer: null,
            showPrivacyPolicy: false,
            timeoutStartTime: null, // 记录倒计时开始时间
            timeoutPausedTime: null, // 记录暂停时的剩余时间
            timeoutDuration: 30000, // 倒计时总时长（30秒）
            privacyPolicyResolve: null // 保存隐私协议Promise的resolve函数
        };
    },
    async created() {},
    async mounted() {
        setTimeout(async () => {
            this.initConfig();
            window.CWorkstationCommunicationMng = CWorkstationCommunicationMng;

            this.startTimeout();

            try {
                const browse_type = await CWorkstationCommunicationMng.init();
                window.Logger.save({
                    message: `browse_type:${browse_type}`,
                    eventType: "init",
                });
                await this.getEnvConfig()
                await this.queryConfig()
                await this.initRegion();
                clearTimeout(this.timeoutTimer);

                // 检查隐私协议
                await this.handleAfterAgreePrivacyPolicy();

                this.$store.commit("globalParams/updateGlobalParams", {
                    init: true,
                });
                this.$nextTick(() => {
                    const redirectPath = this.$route.query.previousPath || '/main/dashboard'
                    this.$router.replace(redirectPath)
                });
            } catch (e) {
                console.error(e);
                window.Logger.saveError({
                    message: `init error:${e}`,
                    eventType: "init",
                });
                clearTimeout(this.timeoutTimer);
                this.loading = false;
                this.timeout = true;
            }
        }, 0);
    },
    methods: {
        initRegion() {
            return new Promise((resolve, reject) => {
                if (Tool.checkAppClient("Cef")) {
                    Tool.createCWorkstationCommunicationMng({
                        name: "getOSInformation",
                        emitName: "NotifyOSInformation",
                        params: {},
                        timeout: 1500,
                    }).then((res) => {
                        console.log("NotifyOSInformation:", res);
                        this.$store.commit("globalParams/updateGlobalParams", {
                            region: res.data.areaData && res.data.areaData.currentArea,
                        });
                        this.getRegionFunctions().then((res) => {
                            resolve(res);
                        });
                    });
                } else {
                    this.getRegionFunctions().then((res) => {
                        resolve(res);
                    });
                }
            });
        },
        getEnvConfig(){
            return new Promise((resolve,reject)=>{
                service.getEnvConfig().then((res)=>{
                    console.log("getEnvConfig:", res);
                    // 将getEnvConfig返回的数据存储到store中
                    if (res && res.data) {
                        // 统一存储envConfig数据
                        this.$store.commit("systemConfig/updateSystemConfig", {
                            envConfig: res.data.data
                        });
                    }
                    resolve(res)
                }).catch(e=>{
                    console.warn("getEnvConfig failed, but continuing with initialization:", e)
                    // 即使失败也resolve，确保不影响后续流程
                    resolve(null)
                })
            })
        },
        queryConfig(){
            return new Promise((resolve,reject)=>{
                const getConfig = (qt=0)=>{
                    let queryTimes = qt||0;
                    service.query_login_config().then((res)=>{
                        this.$store.commit('systemConfig/updateSystemConfig',{
                            serverInfo:res.data
                        })
                        resolve(res)
                    }).catch(e=>{
                        if (queryTimes<100) {
                            setTimeout(()=>{
                                getConfig(++queryTimes);
                            },3000)
                        }else{
                            reject(e)
                        }
                    })
                }
                getConfig()
            })
        },
        getRegionFunctions() {
            return new Promise((resolve, reject) => {
                service
                    .getRegionFunctions({
                        region: this.globalParams.region,
                    })
                    .then((res) => {
                        if (res.data.error_code == 0) {
                            this.$store.commit("globalParams/updateFunctionsStatus", res.data.data);
                        }
                        resolve(res);
                    })
                    .catch((err) => {
                        reject(err);
                    });
            });
        },
        getSystemLanguage() {
            let system_language = navigator.language || navigator.userLanguage;
            console.log("system_language:", system_language);

            if (system_language == "zh-CN") {
                system_language = "CN";
            } else if (system_language.startsWith("en")) {
                system_language = "EN";
            } else if (system_language.startsWith("es")) {
                system_language = "ES";
            } else if (system_language.startsWith("ru")) {
                system_language = "RU";
            } else if (system_language.startsWith("pt")) {
                system_language = "PTBR";
            } else if (system_language.startsWith("de")) {
                system_language = "DE";
            }  else if (system_language.startsWith("fr")) {
                system_language = "FR";
            }  else if (system_language.startsWith("it")) {
                system_language = "IT";
            } else {
                system_language = "";
            }
            return system_language;
        },
        getOSName() {
            let ua = navigator.userAgent.toLowerCase();
            let os_name = "unKnow";
            if (ua.match(/android|adr/i)) {
                os_name = "android";
            } else if (ua.match(/iphone|ipad|ipod/i)) {
                os_name = "ios";
            } else if (ua.match(/(Windows NT|Macintosh|Linux)/i)) {
                os_name = "windows";
            }
            return os_name;
        },
        getServerType() {
            let port = window.location.port ? ":" + window.location.port : "";
            let server_type = {
                hostname: window.location.hostname,
                protocol: window.location.protocol + "//",
                host: window.location.hostname,
                port: port,
                enable_sms_identification: true,
                websocket_protocol: window.location.protocol === "https:" ? "wss://" : "ws://",
                websocket_prot: window.location.protocol === "https:" ? ":443" : port,
            };
            let appVersion = "Prod";
            if (Tool.isDev()) {
                appVersion = "Dev";
            } else if (Tool.isBeta()) {
                appVersion = "Beta";
            } else if (window.location.href.indexOf("localhost") > -1) {
                appVersion = "Dev";
            } else if (!!window.location.hostname.match(/^((25[0-5]|2[0-4]\d|[01]?\d\d?)($|(?!\.$)\.)){4}$/)) {
                server_type.websocket_prot = ":" + window.location.port;
            }
            return server_type;
        },
        initConfig() {
            const osName = this.getOSName();
            this.$store.commit("globalParams/updateGlobalParams", {
                osName: osName,
            });
            const isCE = process.env.VUE_APP_PROJECT_NOV === "CE";
            let lang = "CN";
            if (isCE) {
                lang = "EN";
            }
            let system_language = this.getSystemLanguage();

            let languageStr = window.urlHash.language || window.localStorage.getItem("lang") || system_language || lang;
            let clientTypeStr = window.urlHash.clientType || window.localStorage.getItem("clientType") || "Client";
            window.localStorage.setItem("clientType", clientTypeStr);

            var systemConfig = this.$store.state.systemConfig;

            this.$store.commit("globalParams/updateGlobalParams", {
                isCE: isCE,
            });
            if (isCE) {
                //CE版本处理
                Tool.replaceAppNameToLanguages(languages);
            }
            window.clientType = systemConfig.client_type[clientTypeStr] || systemConfig.client_type.Client;

            setLanguage(languageStr);
            this.$store.commit("systemConfig/updateSystemConfig", {
                clientType: window.clientType,
                server_type: this.getServerType(),
            });
            this.setDesignatedClientType()
        },
        refreshPage() {
            window.location.reload();
        },
        setDesignatedClientType(){
            if(designatedClientType){
                console.error('designatedClientType',designatedClientType)
                window.clientType = designatedClientType
                window.localStorage.setItem("clientType", designatedClientType);
                this.$store.commit("systemConfig/updateSystemConfig", {
                    clientType: window.clientType,
                });
                if(designatedClientType === clientTypeMap.AppWorkstation || designatedClientType === clientTypeMap.AppClient){
                    this.$store.commit('globalParams/updateGlobalParams', {
                        isCef: true,
                    })
                }
            }
        },
        // 开始倒计时
        startTimeout() {
            this.timeoutStartTime = Date.now();
            this.timeoutTimer = setTimeout(() => {
                this.loading = false;
                this.timeout = true;
            }, this.timeoutDuration);
        },
        // 暂停倒计时
        pauseTimeout() {
            if (this.timeoutTimer) {
                clearTimeout(this.timeoutTimer);
                this.timeoutTimer = null;
                // 计算剩余时间
                const elapsed = Date.now() - this.timeoutStartTime;
                this.timeoutPausedTime = Math.max(0, this.timeoutDuration - elapsed);
            }
        },
        // 恢复倒计时
        resumeTimeout() {
            if (this.timeoutPausedTime !== null && this.timeoutPausedTime > 0) {
                this.timeoutStartTime = Date.now();
                this.timeoutTimer = setTimeout(() => {
                    this.loading = false;
                    this.timeout = true;
                }, this.timeoutPausedTime);
                this.timeoutPausedTime = null;
            }
        },
        // 处理隐私协议
        handleAfterAgreePrivacyPolicy(){
            return new Promise((resolve,reject)=>{
                const privacyCheck = checkPrivacyPolicy()

                // 检查是否需要显示隐私协议界面
                // 1. 从未同意过隐私协议 (!hasAgreed)
                // 2. 需要重新同意隐私协议 (needReagree)
                if(!privacyCheck.hasAgreed || privacyCheck.needReagree){
                    // 需要显示隐私协议弹窗，暂停倒计时
                    CWorkstationCommunicationMng.navigationShowNormalOrMaximize();
                    this.pauseTimeout();
                    this.showPrivacyPolicy = true
                    this.privacyPolicyResolve = resolve; // 保存resolve函数
                }else{
                    // 已经同意过且版本号一致，直接放行
                    // 获取当前版本号并通知客户端
                    const privacy_version = this.$store.state.systemConfig.envConfig && this.$store.state.systemConfig.envConfig.privacy_agreement_version
                    window.CWorkstationCommunicationMng.setPrivacyPolicyStatus({
                        status: 1,
                        version: privacy_version
                    })
                    resolve()
                }
            })
        },
        // 隐私协议同意后的回调
        handlePrivacyPolicyAgreed(isAgree) {
            if(isAgree){
                this.showPrivacyPolicy = false
                // 恢复倒计时
                this.resumeTimeout();
                if (this.privacyPolicyResolve) {
                    this.privacyPolicyResolve();
                    this.privacyPolicyResolve = null;
                }
            }
        }
    },
    beforeDestroy() {
        // 清理倒计时定时器
        if (this.timeoutTimer) {
            clearTimeout(this.timeoutTimer);
            this.timeoutTimer = null;
        }
    }
};
</script>

<style scoped lang="scss">
.init-container {
    width: 100%;
    height: 100%;
    background-color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
}

.timeout-ui {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    .timeout-text {
        color: #ff5e5e;
        font-size: 24px;
        font-weight: 500;
        margin-bottom: 32px;
        text-align: center;
        line-height: 1.6;
        letter-spacing: 0.5px;
    }
    .refresh-btn {
        min-width: 120px;
        padding: 12px 32px;
        background: #42a5ff;
        color: #fff;
        border: none;
        border-radius: 8px;
        font-size: 18px;
        font-weight: bold;
        cursor: pointer;
        transition: background 0.2s;
        box-shadow: 0 2px 8px rgba(66, 165, 255, 0.08);
    }

    .refresh-btn:hover {
        background: #1e88e5;
    }
}
</style>
