const CopyWebpackPlugin = require('copy-webpack-plugin');
const IconfontTimestampClearPlugin = require('./plugins/IconfontTimestampClearPlugin.js')
const { defineConfig } = require('@vue/cli-service');
const chalk = require('chalk');
const devModuleList = require('./build/dev-modules');
const mode = process.env.MODE || 'prod';
const path = require('path');
const isProduction = mode === 'prod';
const NodePolyfillPlugin = require('node-polyfill-webpack-plugin')
const {GenerateSW}=require('workbox-webpack-plugin')
const HtmlWebpackPlugin = require('html-webpack-plugin');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
// const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin
// 总的页面
const PAGES = require('./build/pages');
const { proxyTable } = require('./config/proxy');
const moment  = require('moment')
const os = require('os');
const fs = require('fs');
// const { pwa } = require('./config/index');
const getLocalIP = () => {
    //所有的网卡
    const ifaces = os.networkInterfaces();
    let network = [];
    //移除loopback,没多大意义
    Reflect.ownKeys(ifaces).forEach(key => {
      if (!/loopback/ig.test(key)) {
        network = [...network, ...ifaces[key]];
      };
    });
    return network.reduce((arr, { address, family }) => {
      const ip = (/^IPv4$/ig.test(family) ? [address] : []);
      return [...arr, ...ip];
    }, []);
  };


let pages = {};
const moduleName = process.env.MODULE_NAME;
process.env.VUE_APP_BUILD_TIME = `${moment(new Date()).format("YYYY-MM-DD HH:mm")} --${getLocalIP()[0]}`
if (isProduction) {
    // 构建模块的名称
    if (!PAGES[moduleName]) {
        console.log(chalk.red('**模块名不正确**'));
        return;
    }
    pages[moduleName] = PAGES[moduleName];
} else {
    // 本地开发编译的模块
    // 编译全部
    if (process.env.DEV_MODULE === 'all') {
        pages = PAGES;
    } else {
    // 编译部分模块
        const moduleList = [
            // 自定义编译的模块
            ...devModuleList,
        ];
        moduleList.forEach((item) => {
            pages[item] = PAGES[item];
        });
    }
}
const shouldRemoveHash = false
module.exports = defineConfig({
    transpileDependencies: true,
    // 这行代码很重要
    publicPath: isProduction ? './' : '/',
    pages,
    // 这行代码很重要
    outputDir: isProduction ? `dist_${process.env.VUE_APP_PROJECT_NOV}/${moduleName}` : 'dist',
    assetsDir: 'static',
    css: {
        loaderOptions: {
            sass: {
                sassOptions: { outputStyle: "expanded" }
            }
        },
        extract:isProduction
    },
    devServer:{
        open:['http://localhost:8888/ultrasync_pc.html#/index'],
        port:8888,
        proxy:proxyTable,
        // 根据浏览器agent或设备类型重定向到对应页面
        onBeforeSetupMiddleware: function(devServer) {
          devServer.app.get('/', function(req, res) {
            const userAgent = req.headers['user-agent'] || '';

            // 检测是否为移动设备
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
            if (isMobile) {
              res.redirect('/ultrasync.html');
            } else {
              res.redirect('/ultrasync_pc.html');
            }
          });
        },
        allowedHosts: [
            'f4t5840647.goho.co', // 允许访问的域名地址，即花生壳内网穿透的地址 1
          ],
    },
    runtimeCompiler:true,
    chainWebpack: (config) => {
        // 配置SVG loader
        config.module
            .rule('svg')
            .exclude.add(path.resolve('src/icons'))
            .end();

        config.module
            .rule('icons')
            .test(/\.svg$/)
            .include.add(path.resolve('src/icons'))
            .end()
            .use('svg-sprite-loader')
            .loader('svg-sprite-loader')
            .options({
                symbolId: 'icon-[name]'
            })
            .end();

        config.optimization.splitChunks({
            cacheGroups: {
                vue: {
                    name: 'chunk-vue',
                    test: /[\\/]node_modules[\\/]_?(vue|lodash|vue-router|vuex|element-ui)(@.*)?[\\/]/,
                    priority: 30,
                    chunks: 'initial',
                },
                // 新增 AWS 相关依赖打包
                aws: {
                    name: 'chunk-aws',
                    test: /[\\/]node_modules[\\/]_?aws-sdk[\\/]/,
                    priority: -1,
                    chunks: 'all',
                },
                // 新增阿里云 OSS 相关依赖打包
                alioss: {
                    name: 'chunk-alioss',
                    test: /[\\/]node_modules[\\/]_?ali-oss[\\/]/,
                    priority: -1,
                    chunks: 'all',
                },
                // 新增 Agora 相关依赖打包
                agora: {
                    name: 'chunk-agora',
                    test: /[\\/]node_modules[\\/]_?agora-rtc-sdk-ng[\\/]/,
                    priority: -1,
                    chunks: 'all',
                },
                // 新增 Aegis 相关依赖打包
                aegis: {
                    name: 'chunk-aegis',
                    test: /[\\/]node_modules[\\/]_?aegis-web-sdk[\\/]/,
                        priority: -1,
                    chunks: 'all',
                },
                vendors: {
                    name: 'chunk-vendors',
                    test: /[\\/]node_modules[\\/]/,
                    priority: -10,
                    chunks: 'initial',
                },
                common: {
                    name: 'chunk-common',
                    minChunks: 2,
                    priority: -20,
                    chunks: 'initial',
                    reuseExistingChunk: true,
                },
                // 新增 white-web-sdk 相关依赖打包
                whiteboard: {
                    name: 'chunk-whiteboard',
                    test: /[\\/]node_modules[\\/]_?white-web-sdk[\\/]/,
                    priority: -1,
                    chunks: 'all',
                    enforce: true
                },
                // 新增 xlsx 相关依赖打包
                xlsx: {
                    name: 'chunk-xlsx',
                    test: /[\\/]node_modules[\\/]_?xlsx[\\/]/,
                    priority: -1,
                    chunks: 'all',
                    enforce: true
                },
                // 新增 echarts 相关依赖打包
                echarts: {
                    name: 'chunk-echarts',
                    test: /[\\/]node_modules[\\/]_?echarts[\\/]/,
                    priority: -1,
                    chunks: 'all',
                    enforce: true
                },
                // 新增 PDF.js 相关依赖打包
                pdfh5: {
                    name: 'chunk-pdfh5',
                    test: /[\\/]node_modules[\\/]_?pdfh5[\\/]/,
                    priority: -1,
                    chunks: 'all',
                    enforce: true
                },
                // 新增 hls.js 相关依赖打包
                hlsjs: {
                    name: 'chunk-hlsjs',
                    test: /[\\/]node_modules[\\/]_?hls\.js[\\/]/,
                    priority: -1,
                    chunks: 'all',
                    enforce: true
                },
                // 新增 highlight.js 相关依赖打包
                highlightjs: {
                    name: 'chunk-highlightjs',
                    test: /[\\/]node_modules[\\/]_?highlight\.js[\\/]/,
                    priority: -1,
                    chunks: 'all',
                    enforce: true
                },
            },
        });
        if (shouldRemoveHash) {
            // Define the plugin first before calling .tap()
            config.plugin('extract-css')
                .use(MiniCssExtractPlugin, [{
                    filename: 'static/css/[name].css',
                    chunkFilename: 'static/css/[name].css'
                }]);

            // Call .tap() to modify the plugin's options
            config.plugin('extract-css')
            .tap(args => {
                args[0].filename = 'static/css/[name].css';
                args[0].chunkFilename = 'static/css/[name].css';
                return args;
            });
        }
        isLoadCopyWebpackPlugin()&&config.plugin('CopyWebpackPlugin')
        .use(CopyWebpackPlugin, [isProduction ? {
          patterns: [
            {
              from: path.resolve(__dirname, getResourceDir('from')),  // 使用重构后的方法
              to: getResourceDir('to'),
              info: { minimized: true },
            },
          ],
        } : {
          patterns: [
            {
              from: path.resolve(__dirname, getResourceDir('from', 'ultrasync')),
              to: getResourceDir('to', 'ultrasync'),
            },
            {
              from: path.resolve(__dirname, getResourceDir('from', 'ultrasync_pc')),
              to: getResourceDir('to', 'ultrasync_pc'),
            },
            {
              from: path.resolve(__dirname, getResourceDir('from', 'audit')),
              to: getResourceDir('to', 'audit'),
            },
            {
              from: path.resolve(__dirname, getResourceDir('from', 'activity')),
              to: getResourceDir('to', 'activity'),
            }
          ],
        }]);
    },
    configureWebpack: {
        devtool: isProduction ? "cheap-module-source-map" : "eval-cheap-module-source-map",
        resolve: {
            extensions: ['.js', '.vue', '.json'],
            alias: {
                '@': path.resolve('src')
            },
        },
        output: {
            filename: shouldRemoveHash ? 'static/js/[name].js' : 'static/js/[name].[contenthash:8].js',
            chunkFilename: shouldRemoveHash ? 'static/js/[name].js' : 'static/js/[name].[contenthash:8].js',
        },
        plugins: [
            {
                apply: (compiler) => {
                    isProduction&&compiler.hooks.done.tap('WriteBuildTimePlugin', (stats) => {
                    const buildTime = process.env.VUE_APP_BUILD_TIME;
                    const buildTimeContent = `// Build Time\nexport const buildTime = '${buildTime}';`;

                    // 写入 buildTime.js 到 dist 目录
                    const outputPath = path.resolve(__dirname, `dist_${process.env.VUE_APP_PROJECT_NOV}/${moduleName}`, 'buildTime.js');
                    fs.writeFileSync(outputPath, buildTimeContent, 'utf8');
                  });
                },
              },
            shouldRemoveHash&&new MiniCssExtractPlugin({
                filename: 'static/css/[name].css',
                chunkFilename: 'static/css/[name].css'
              }),
            new NodePolyfillPlugin(),
            new IconfontTimestampClearPlugin(),
            // new BundleAnalyzerPlugin()
        ].filter(Boolean)
    }
});
// 定义资源路径映射表
const resourceMap = {
  ultrasync: 'static/resource',
  ultrasync_pc: 'static/resource_pc',
  audit: 'static/resource_audit',
  activity: 'static/resource_activity',
};

// 获取路径
function getResourceDir(type, target = moduleName) {
  if (!resourceMap[target]) {
    console.error(`模块 ${target} 不存在于资源映射表中!`);
    return '';
  }
  const basePath = resourceMap[target];

  // 返回不同的路径，根据类型是 'from' 还是 'to'
  return type === 'from' ? `./${basePath}` : basePath;
}
const unLoadCopyModules = ['whiteboard']
function isLoadCopyWebpackPlugin(){
    return !unLoadCopyModules.includes(moduleName)
}

