/**
 * 分阶段初始化测试文件
 * 测试权限管理器的分阶段初始化功能
 */

import permissionManager from './index.js';

/**
 * 模拟 Vuex store 和全局变量
 */
function setupMockEnvironment() {
    // 模拟 window.vm 和 $store
    window.vm = {
        $store: {
            state: {
                globalParams: {
                    region: 'CN',
                    isCE: false,
                    functionsStatus: {
                        live: 1,
                        library: 1,
                        cloudStatistic: 1,
                        breastCases: 1,
                        breastAI: 1,
                        drAIAssistant: 1,
                        groupset: 1,
                        wechat: 1,
                        obstetricalAI: 1,
                        tvwall: 1,
                        qcStatistics: 1,
                        referralCode: 1,
                        webShareScreen: 1,
                        webTvwallEnterConversation: 1,
                        ai: 1,
                        smartEdTechTraining: 1,
                        ultrasoundQCReport: 1,
                        club: 1,
                        professionalIdentityForce: 0
                    }
                }
            },
            commit: function(mutation, payload) {
                console.log(`Mock commit: ${mutation}`, payload);
                if (mutation === 'globalParams/updateFunctionsStatus') {
                    Object.assign(this.state.globalParams.functionsStatus, payload);
                }
            },
            watch: function(getter, callback, options) {
                // 模拟 store.watch 方法
                console.log('Mock store.watch called');
                return function unwatch() {
                    console.log('Mock unwatch called');
                };
            }
        }
    };
}

/**
 * 测试第一阶段初始化（区域权限）
 */
async function testStage1Initialization() {
    console.log('=== 测试第一阶段初始化（区域权限） ===');
    
    // 设置模拟环境
    setupMockEnvironment();
    
    try {
        // 第一阶段：只初始化区域权限管理器
        await permissionManager.initializeRegionPermissions();
        console.log('✓ 区域权限管理器初始化成功');
        
        // 检查初始化状态
        console.log('整体初始化状态:', permissionManager.isInitialized());
        console.log('区域权限初始化状态:', permissionManager.regionInitialized);
        
        // 测试区域权限检查
        console.log('\n--- 测试区域权限检查 ---');
        const testFunctions = ['live', 'tvwall', 'breastAI', 'professionalIdentityForce'];
        testFunctions.forEach(func => {
            const hasPermission = permissionManager.checkRegionPermission(func);
            console.log(`${func}: ${hasPermission}`);
        });
        
        // 测试其他权限管理器（应该返回 false，因为还未初始化）
        console.log('\n--- 测试其他权限管理器（应该返回 false） ---');
        console.log('路由权限:', permissionManager.checkRoutePermission('/admin'));
        console.log('组件权限:', permissionManager.checkComponentPermission('menu', 'admin'));
        console.log('功能权限:', permissionManager.checkFeaturePermission('userManage'));
        
    } catch (error) {
        console.error('✗ 第一阶段初始化失败:', error);
    }
}

/**
 * 测试第二阶段初始化（用户权限）
 */
async function testStage2Initialization() {
    console.log('\n=== 测试第二阶段初始化（用户权限） ===');
    
    const userInfo = {
        uid: 'test-user-123',
        role: 2,
        type: 1,
        nickname: '测试用户'
    };
    
    try {
        // 第二阶段：初始化用户相关权限管理器
        await permissionManager.initialize(userInfo);
        console.log('✓ 用户权限管理器初始化成功');
        
        // 检查初始化状态
        console.log('整体初始化状态:', permissionManager.isInitialized());
        console.log('区域权限初始化状态:', permissionManager.regionInitialized);
        
        // 测试所有权限管理器
        console.log('\n--- 测试所有权限管理器 ---');
        console.log('区域权限 live:', permissionManager.checkRegionPermission('live'));
        console.log('路由权限 /admin:', permissionManager.checkRoutePermission('/admin'));
        console.log('组件权限 menu.admin:', permissionManager.checkComponentPermission('menu', 'admin'));
        console.log('功能权限 userManage:', permissionManager.checkFeaturePermission('userManage'));
        
        // 测试通用权限检查
        console.log('\n--- 测试通用权限检查 ---');
        console.log('live (区域权限):', permissionManager.checkPermission('live'));
        console.log('backgroundManage (预定义权限):', permissionManager.checkPermission('backgroundManage'));
        
    } catch (error) {
        console.error('✗ 第二阶段初始化失败:', error);
    }
}

/**
 * 测试权限管理器状态检查
 */
function testManagerStates() {
    console.log('\n=== 测试权限管理器状态检查 ===');
    
    // 测试获取子管理器
    const routeManager = permissionManager.getManager('route');
    const componentManager = permissionManager.getManager('component');
    const featureManager = permissionManager.getManager('feature');
    const regionManager = permissionManager.getManager('region');
    
    console.log('RouteManager 存在:', !!routeManager);
    console.log('ComponentManager 存在:', !!componentManager);
    console.log('FeatureManager 存在:', !!featureManager);
    console.log('RegionManager 存在:', !!regionManager);
    
    // 测试区域权限相关方法
    console.log('\n--- 测试区域权限相关方法 ---');
    console.log('当前区域:', permissionManager.getCurrentRegion());
    console.log('启用的区域功能:', permissionManager.getEnabledRegionFunctions());
    console.log('breastAI 可用性:', permissionManager.isRegionFunctionAvailable('breastAI', { checkCE: true }));
}

/**
 * 测试动态更新功能
 */
function testDynamicUpdate() {
    console.log('\n=== 测试动态更新功能 ===');
    
    // 模拟从服务器获取新的 functionsStatus
    const newFunctionsStatus = {
        live: 0,
        tvwall: 0,
        breastAI: 1,
        ai: 1
    };
    
    console.log('更新前 live 权限:', permissionManager.checkRegionPermission('live'));
    console.log('更新前 tvwall 权限:', permissionManager.checkRegionPermission('tvwall'));
    
    // 模拟 store 更新
    window.vm.$store.commit('globalParams/updateFunctionsStatus', newFunctionsStatus);
    
    // 等待一下让监听器处理更新
    setTimeout(() => {
        console.log('更新后 live 权限:', permissionManager.checkRegionPermission('live'));
        console.log('更新后 tvwall 权限:', permissionManager.checkRegionPermission('tvwall'));
        console.log('更新后 breastAI 权限:', permissionManager.checkRegionPermission('breastAI'));
    }, 100);
}

/**
 * 运行所有测试
 */
async function runAllTests() {
    console.log('开始运行分阶段初始化测试...\n');
    
    try {
        await testStage1Initialization();
        await testStage2Initialization();
        testManagerStates();
        testDynamicUpdate();
        
        console.log('\n✓ 所有测试完成');
    } catch (error) {
        console.error('\n✗ 测试过程中出现错误:', error);
    }
}

// 如果在浏览器环境中运行，自动执行测试
if (typeof window !== 'undefined') {
    // 延迟执行，确保模块加载完成
    setTimeout(runAllTests, 100);
}

export {
    setupMockEnvironment,
    testStage1Initialization,
    testStage2Initialization,
    testManagerStates,
    testDynamicUpdate,
    runAllTests
};
