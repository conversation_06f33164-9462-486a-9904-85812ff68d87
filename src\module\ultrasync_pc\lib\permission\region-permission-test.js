/**
 * 区域权限管理器测试文件
 * 用于验证区域权限管理器的功能
 */

import permissionManager from './index.js';

/**
 * 模拟 Vuex store 和全局变量
 */
function setupMockEnvironment() {
    // 模拟 window.vm 和 $store
    window.vm = {
        $store: {
            state: {
                globalParams: {
                    region: 'CN',
                    isCE: false,
                    functionsStatus: {
                        live: 1,
                        library: 1,
                        cloudStatistic: 1,
                        breastCases: 1,
                        breastAI: 1,
                        drAIAssistant: 1,
                        groupset: 1,
                        wechat: 1,
                        obstetricalAI: 1,
                        tvwall: 1,
                        qcStatistics: 1,
                        referralCode: 1,
                        webShareScreen: 1,
                        webTvwallEnterConversation: 1,
                        ai: 1,
                        smartEdTechTraining: 1,
                        ultrasoundQCReport: 1,
                        club: 1,
                        professionalIdentityForce: 0
                    }
                }
            },
            commit: function(mutation, payload) {
                console.log(`Mock commit: ${mutation}`, payload);
                if (mutation === 'globalParams/updateFunctionsStatus') {
                    Object.assign(this.state.globalParams.functionsStatus, payload);
                }
            }
        }
    };
}

/**
 * 测试区域权限管理器基本功能
 */
async function testBasicRegionPermissions() {
    console.log('=== 测试区域权限管理器基本功能 ===');
    
    // 设置模拟环境
    setupMockEnvironment();
    
    // 初始化权限管理器
    const userInfo = {
        uid: 'test-user-123',
        role: 2,
        type: 1,
        nickname: '测试用户'
    };
    
    try {
        await permissionManager.initialize(userInfo);
        console.log('✓ 权限管理器初始化成功');
    } catch (error) {
        console.error('✗ 权限管理器初始化失败:', error);
        return;
    }
    
    // 测试区域功能权限检查
    console.log('\n--- 测试区域功能权限检查 ---');
    
    const testFunctions = ['live', 'tvwall', 'breastAI', 'cloudStatistic', 'professionalIdentityForce'];
    
    testFunctions.forEach(func => {
        const hasPermission = permissionManager.checkRegionPermission(func);
        const isEnabled = permissionManager.isRegionFunctionEnabled(func);
        console.log(`${func}: 权限=${hasPermission}, 启用=${isEnabled}`);
    });
    
    // 测试获取启用的功能列表
    console.log('\n--- 测试获取启用的功能列表 ---');
    const enabledFunctions = permissionManager.getEnabledRegionFunctions();
    console.log('启用的功能:', enabledFunctions);
    
    // 测试获取当前区域
    console.log('\n--- 测试获取当前区域 ---');
    const currentRegion = permissionManager.getCurrentRegion();
    console.log('当前区域:', currentRegion);
    
    // 测试CE区域限制
    console.log('\n--- 测试CE区域限制 ---');
    const breastCasesAvailable = permissionManager.isRegionFunctionAvailable('breastCases', { checkCE: true });
    const cloudStatisticAvailable = permissionManager.isRegionFunctionAvailable('cloudStatistic', { checkCE: true });
    console.log('乳腺病例库可用:', breastCasesAvailable);
    console.log('云端统计可用:', cloudStatisticAvailable);
    
    // 模拟CE区域
    window.vm.$store.state.globalParams.isCE = true;
    const breastCasesAvailableCE = permissionManager.isRegionFunctionAvailable('breastCases', { checkCE: true });
    const cloudStatisticAvailableCE = permissionManager.isRegionFunctionAvailable('cloudStatistic', { checkCE: true });
    console.log('CE区域 - 乳腺病例库可用:', breastCasesAvailableCE);
    console.log('CE区域 - 云端统计可用:', cloudStatisticAvailableCE);
    
    // 恢复非CE区域
    window.vm.$store.state.globalParams.isCE = false;
}

/**
 * 测试权限更新功能
 */
async function testPermissionUpdate() {
    console.log('\n=== 测试权限更新功能 ===');
    
    const regionManager = permissionManager.getManager('region');
    
    // 更新部分功能权限
    const newFunctions = {
        live: 0,
        tvwall: 0,
        breastAI: 1
    };
    
    console.log('更新前的权限状态:');
    console.log('直播:', permissionManager.checkRegionPermission('live'));
    console.log('电视墙:', permissionManager.checkRegionPermission('tvwall'));
    console.log('乳腺AI:', permissionManager.checkRegionPermission('breastAI'));
    
    regionManager.updateRegionFunctions(newFunctions);
    
    console.log('\n更新后的权限状态:');
    console.log('直播:', permissionManager.checkRegionPermission('live'));
    console.log('电视墙:', permissionManager.checkRegionPermission('tvwall'));
    console.log('乳腺AI:', permissionManager.checkRegionPermission('breastAI'));
}

/**
 * 测试通用权限检查方法
 */
function testUniversalPermissionCheck() {
    console.log('\n=== 测试通用权限检查方法 ===');
    
    // 测试区域功能权限通过通用方法检查
    const testFunctions = ['live', 'tvwall', 'breastAI', 'unknownFunction'];
    
    testFunctions.forEach(func => {
        const hasPermission = permissionManager.checkPermission(func);
        console.log(`通用权限检查 ${func}: ${hasPermission}`);
    });
}

/**
 * 测试错误处理
 */
function testErrorHandling() {
    console.log('\n=== 测试错误处理 ===');
    
    const regionManager = permissionManager.getManager('region');
    
    // 测试未知功能
    const unknownPermission = regionManager.hasPermission('unknownFunction');
    console.log('未知功能权限检查:', unknownPermission);
    
    // 测试错误信息
    const errorMessage = regionManager.getPermissionErrorMessage('unknownFunction');
    console.log('错误信息:', errorMessage);
    
    const disabledErrorMessage = regionManager.getPermissionErrorMessage('professionalIdentityForce');
    console.log('禁用功能错误信息:', disabledErrorMessage);
}

/**
 * 运行所有测试
 */
async function runAllTests() {
    console.log('开始运行区域权限管理器测试...\n');
    
    try {
        await testBasicRegionPermissions();
        await testPermissionUpdate();
        testUniversalPermissionCheck();
        testErrorHandling();
        
        console.log('\n✓ 所有测试完成');
    } catch (error) {
        console.error('\n✗ 测试过程中出现错误:', error);
    }
}

// 如果在浏览器环境中运行，自动执行测试
if (typeof window !== 'undefined') {
    // 延迟执行，确保模块加载完成
    setTimeout(runAllTests, 100);
}

export {
    setupMockEnvironment,
    testBasicRegionPermissions,
    testPermissionUpdate,
    testUniversalPermissionCheck,
    testErrorHandling,
    runAllTests
};
