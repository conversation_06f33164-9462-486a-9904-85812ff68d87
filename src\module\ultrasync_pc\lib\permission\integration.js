/**
 * 权限管理器集成文件
 * 用于将现有的权限逻辑迁移到新的权限管理器系统中
 */

import permissionManager from './index.js';



/**
 * 从存储中获取token
 */
function getTokenFromStorage() {
    // 使用现有的Tool.getToken方法
    if (window.Tool && window.Tool.getToken) {
        return window.Tool.getToken();
    }

    // 备用方案
    return localStorage.getItem('token') || sessionStorage.getItem('token');
}

/**
 * 从token获取用户信息
 */
function getUserInfoFromToken(token) {
    // 这里应该调用API获取用户信息，暂时返回基础信息
    try {
        // 如果有解析token的方法，可以在这里使用
        // 或者从其他全局变量获取
        if (window.vm && window.vm.$store && window.vm.$store.state.user) {
            return window.vm.$store.state.user;
        }

        // 返回基础用户信息
        return {
            uid: 'unknown',
            role: 1,
            type: 1,
            token: token
        };
    } catch (error) {
        console.error('解析用户信息失败:', error);
        return null;
    }
}

/**
 * 获取多中心配置
 */
function getMulticenterConfig() {
    if (window.vm && window.vm.$store && window.vm.$store.state.multicenter) {
        return window.vm.$store.state.multicenter.config;
    }
    return {};
}
/**
 * 初始化权限管理器
 * 从现有的store和全局变量中获取用户信息
 */
export function initializePermissionManager() {
    try {
        // 从store中获取用户信息
        let userInfo = null;

        if (window.vm && window.vm.$store && window.vm.$store.state.user) {
            userInfo = window.vm.$store.state.user;
        }

        // 如果store中没有用户信息，尝试从其他地方获取
        if (!userInfo || !userInfo.uid) {
            // 从localStorage获取token，然后获取用户信息
            const token = getTokenFromStorage();
            if (token) {
                userInfo = getUserInfoFromToken(token);
            }
        }

        if (userInfo && userInfo.uid) {
            // 初始化权限管理器
            return permissionManager.initialize(userInfo, {
                // 从现有配置中获取多中心配置
                multicenterConfig: getMulticenterConfig(),
                // 其他配置
                debug: process.env.NODE_ENV === 'development'
            });
        } else {
            console.warn('无法获取用户信息，权限管理器未初始化');
            return Promise.resolve();
        }
    } catch (error) {
        console.error('初始化权限管理器失败:', error);
        return Promise.reject(error);
    }
}
/**
 * 集成到现有的路由守卫中
 */
export function integrateWithRouter(router) {
    // 保存原有的beforeEach守卫
    const originalBeforeEach = router.beforeEach;

    // 添加权限检查的路由守卫
    router.beforeEach((to, from, next) => {
        // 检查是否是iframe内的路由变化
        if (window.self !== window.top) {
            next();
            return;
        }

        // 检查是否完成初始化
        const isInit = window.Tool && window.Tool.isInit ? window.Tool.isInit() : true;
        if (to.path.startsWith('/init')) {
            next();
            return;
        }

        if (!isInit) {
            const whiteList = ['/login', '/webLive', '/cloudVideoEditChild', '/pacs_login'];
            if (whiteList.some(path => to.path.indexOf(path) > -1)) {
                const fullPath = to.fullPath || to.path;
                next(`/init?previousPath=${encodeURIComponent(fullPath)}`);
            } else {
                next('/init');
            }
            return;
        }

        // 使用权限管理器检查路由权限
        if (permissionManager.isInitialized()) {
            if (permissionManager.checkRoutePermission(to.path)) {
                next();
            } else {
                const redirectRoute = permissionManager.getRedirectRoute(to.path);
                next(redirectRoute);
            }
        } else {
            // 如果权限管理器未初始化，使用原有逻辑
            if (originalBeforeEach) {
                originalBeforeEach(to, from, next);
            } else {
                // 基本的登录检查
                const token = getTokenFromStorage();
                if (!token) {
                    next('/login');
                } else {
                    next();
                }
            }
        }
    });
}

/**
 * 集成到Vue组件中
 */
export function integrateWithVue(Vue) {
    // 添加全局混入
    Vue.mixin({
        created() {
            // 监听用户信息变化
            if (this.$store) {
                this.$watch('$store.state.user', (newUser, oldUser) => {
                    if (newUser && newUser.uid && permissionManager.isInitialized()) {
                        permissionManager.updateUserInfo(newUser);
                    }
                }, { deep: true });
            }
        },

        methods: {
            // 兼容现有的权限检查方法
            isAdmin() {
                if (permissionManager.isInitialized()) {
                    return permissionManager.isAdmin();
                }
                // 使用原有逻辑
                const user = this.$store && this.$store.state.user;
                return user && (user.role === 2 || user.role === 3 || user.role === 5);
            },

            // 检查菜单权限（兼容现有的leftSideBar组件）
            checkMenuPermission(menuName) {
                if (permissionManager.isInitialized()) {
                    return permissionManager.checkComponentPermission('menu', menuName);
                }
                // 使用原有逻辑
                if (menuName === 'background_manage') {
                    return this.isAdmin();
                }
                return true;
            }
        }
    });
}



/**
 * 迁移多中心权限配置
 */
function migrateMulticenterPermissions() {
    const multicenterConfig = getMulticenterConfig();

    Object.entries(multicenterConfig).forEach(([type, config]) => {
        if (config.roleRoute) {
            // 添加多中心路由权限
            Object.entries(config.roleRoute).forEach(([role, route]) => {
                const routePath = `/multicenter/${route}`;
                permissionManager.getManager('route').addRoutePermission(routePath, {
                    roles: [parseInt(role)],
                    permissions: [`multicenter_${type}_${route}`]
                });
            });
        }
    });
}

/**
 * 迁移菜单权限配置
 */
function migrateMenuPermissions() {
    // 基于现有的leftSideBar组件逻辑
    const menuPermissions = {
        'system_setting': { roles: [1, 2, 3, 4, 5, 6], permissions: ['user'] },
        'system_info': { roles: [1, 2, 3, 4, 5, 6], permissions: ['user'] },
        'background_manage': { roles: [2, 3, 5], permissions: ['admin'] }
    };

    permissionManager.getManager('component').addComponentPermission('menu', menuPermissions);
}

/**
 * 迁移其他权限配置
 */
function migrateOtherPermissions() {
    // 可以在这里添加其他现有权限逻辑的迁移
    console.log('其他权限配置迁移完成');
}
/**
 * 迁移现有的权限配置
 */
export function migrateExistingPermissions() {
    if (!permissionManager.isInitialized()) {
        console.warn('权限管理器未初始化，无法迁移权限配置');
        return;
    }

    // 迁移多中心权限配置
    migrateMulticenterPermissions();

    // 迁移菜单权限配置
    migrateMenuPermissions();

    // 迁移其他权限配置
    migrateOtherPermissions();
}
/**
 * 创建权限检查装饰器
 */
export function createPermissionDecorator(type, permission, action = null) {
    return function(target, propertyKey, descriptor) {
        const originalMethod = descriptor.value;

        descriptor.value = function(...args) {
            let hasPermission = false;

            switch (type) {
            case 'route':
                hasPermission = permissionManager.checkRoutePermission(permission);
                break;
            case 'component':
                hasPermission = permissionManager.checkComponentPermission(permission, action);
                break;
            case 'feature':
                hasPermission = permissionManager.checkFeaturePermission(permission, action);
                break;
            default:
                hasPermission = true;
            }

            if (hasPermission) {
                return originalMethod.apply(this, args);
            } else {
                console.warn(`权限不足: ${type}:${permission}${action ? ':' + action : ''}`);
                return null;
            }
        };

        return descriptor;
    };
}

/**
 * 权限检查高阶组件
 */
export function withPermission(WrappedComponent, permissionConfig) {
    return {
        name: `WithPermission(${WrappedComponent.name})`,
        props: WrappedComponent.props,
        render(h) {
            const { type, permission, action, context = {} } = permissionConfig;

            let hasPermission = false;

            switch (type) {
            case 'route':
                hasPermission = permissionManager.checkRoutePermission(permission, context);
                break;
            case 'component':
                hasPermission = permissionManager.checkComponentPermission(permission, action, context);
                break;
            case 'feature':
                hasPermission = permissionManager.checkFeaturePermission(permission, action, context);
                break;
            default:
                hasPermission = true;
            }

            if (hasPermission) {
                return h(WrappedComponent, {
                    props: this.$props,
                    attrs: this.$attrs,
                    on: this.$listeners
                }, this.$slots.default);
            } else {
                return h('div', { class: 'permission-denied' }, '权限不足');
            }
        }
    };
}

// 自动初始化（如果在浏览器环境中）
if (typeof window !== 'undefined') {
    // 等待Vue实例创建完成后初始化
    document.addEventListener('DOMContentLoaded', () => {
        setTimeout(() => {
            initializePermissionManager().catch(error => {
                console.error('自动初始化权限管理器失败:', error);
            });
        }, 1000);
    });
}
