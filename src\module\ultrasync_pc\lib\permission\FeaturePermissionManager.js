import BasePermissionManager from './BasePermissionManager.js';

/**
 * 功能权限管理器
 * 负责具体功能操作的权限控制
 */
class FeaturePermissionManager extends BasePermissionManager {
    constructor() {
        super();
        this.featurePermissions = new Map(); // 功能权限配置
        this.apiPermissions = new Map(); // API权限配置
        this.dataPermissions = new Map(); // 数据权限配置
    }

    /**
     * 加载权限数据
     */
    async loadPermissions() {
        this.loadFeaturePermissions();
        this.loadApiPermissions();
        this.loadDataPermissions();
    }

    /**
     * 加载功能权限配置
     */
    loadFeaturePermissions() {
        const featurePermissions = {
            // 用户管理功能
            'user_management': {
                'create_user': { roles: [2, 3, 5], permissions: ['user_create'] },
                'edit_user': { roles: [2, 3, 5], permissions: ['user_edit'] },
                'delete_user': { roles: [3, 5], permissions: ['user_delete'] },
                'view_user': { roles: [1, 2, 3, 4, 5, 6], permissions: ['user_view'] },
                'change_user_role': { roles: [3, 5], permissions: ['user_role_change'] }
            },

            // 群组管理功能
            'group_management': {
                'create_group': { roles: [2, 3, 5], permissions: ['group_create'] },
                'edit_group': { roles: [2, 3, 5], permissions: ['group_edit'] },
                'delete_group': { roles: [3, 5], permissions: ['group_delete'] },
                'manage_members': { roles: [2, 3, 5], permissions: ['group_member_manage'] },
                'set_managers': { roles: [3, 5], permissions: ['group_manager_set'] }
            },

            // 多中心功能
            'multicenter': {
                'create_exam': { roles: [1, 2, 3, 4, 5, 6], permissions: ['exam_create'] },
                'edit_exam': { roles: [1, 2, 3, 4, 5, 6], permissions: ['exam_edit'] },
                'delete_exam': { roles: [2, 3, 5], permissions: ['exam_delete'] },
                'assign_exam': { roles: [2, 3, 5], permissions: ['exam_assign'] },
                'review_exam': { roles: [3, 4], permissions: ['exam_review'] },
                'judge_exam': { roles: [4], permissions: ['exam_judge'] },
                'view_statistics': { roles: [2, 3, 5], permissions: ['statistics_view'] }
            },

            // 文件管理功能
            'file_management': {
                'upload_file': { roles: [1, 2, 3, 4, 5, 6], permissions: ['file_upload'] },
                'download_file': { roles: [1, 2, 3, 4, 5, 6], permissions: ['file_download'] },
                'delete_file': { roles: [2, 3, 5], permissions: ['file_delete'] },
                'share_file': { roles: [1, 2, 3, 4, 5, 6], permissions: ['file_share'] },
                'export_data': { roles: [2, 3, 5], permissions: ['data_export'] }
            },

            // 会议功能
            'conference': {
                'create_meeting': { roles: [1, 2, 3, 4, 5, 6], permissions: ['meeting_create'] },
                'join_meeting': { roles: [1, 2, 3, 4, 5, 6], permissions: ['meeting_join'] },
                'manage_meeting': { roles: [2, 3, 5], permissions: ['meeting_manage'] },
                'record_meeting': { roles: [2, 3, 5], permissions: ['meeting_record'] },
                'live_stream': { roles: [2, 3, 5], permissions: ['live_stream'] }
            },

            // 教育功能
            'education': {
                'create_course': { roles: [2, 3, 5], permissions: ['course_create'] },
                'edit_course': { roles: [2, 3, 5], permissions: ['course_edit'] },
                'delete_course': { roles: [3, 5], permissions: ['course_delete'] },
                'view_course': { roles: [1, 2, 3, 4, 5, 6], permissions: ['course_view'] },
                'manage_homework': { roles: [2, 3, 5], permissions: ['homework_manage'] }
            },

            // 系统设置功能
            'system_settings': {
                'view_settings': { roles: [1, 2, 3, 4, 5, 6], permissions: ['settings_view'] },
                'edit_settings': { roles: [2, 3, 5], permissions: ['settings_edit'] },
                'system_info': { roles: [1, 2, 3, 4, 5, 6], permissions: ['system_info_view'] },
                'background_manage': { roles: [2, 3, 5], permissions: ['background_manage'] }
            }
        };

        Object.entries(featurePermissions).forEach(([feature, permissions]) => {
            this.featurePermissions.set(feature, permissions);
        });
    }

    /**
     * 加载API权限配置
     */
    loadApiPermissions() {
        const apiPermissions = {
            // 用户相关API
            'POST /api/user/create': { roles: [2, 3, 5], permissions: ['user_create'] },
            'PUT /api/user/update': { roles: [2, 3, 5], permissions: ['user_edit'] },
            'DELETE /api/user/delete': { roles: [3, 5], permissions: ['user_delete'] },
            'GET /api/user/list': { roles: [1, 2, 3, 4, 5, 6], permissions: ['user_view'] },
            'POST /api/user/role': { roles: [3, 5], permissions: ['user_role_change'] },

            // 群组相关API
            'POST /api/group/create': { roles: [2, 3, 5], permissions: ['group_create'] },
            'PUT /api/group/update': { roles: [2, 3, 5], permissions: ['group_edit'] },
            'DELETE /api/group/delete': { roles: [3, 5], permissions: ['group_delete'] },
            'POST /api/group/members': { roles: [2, 3, 5], permissions: ['group_member_manage'] },

            // 多中心相关API
            'POST /api/multicenter/exam': { roles: [1, 2, 3, 4, 5, 6], permissions: ['exam_create'] },
            'PUT /api/multicenter/exam': { roles: [1, 2, 3, 4, 5, 6], permissions: ['exam_edit'] },
            'DELETE /api/multicenter/exam': { roles: [2, 3, 5], permissions: ['exam_delete'] },
            'POST /api/multicenter/assign': { roles: [2, 3, 5], permissions: ['exam_assign'] },
            'POST /api/multicenter/review': { roles: [3, 4], permissions: ['exam_review'] },

            // 文件相关API
            'POST /api/file/upload': { roles: [1, 2, 3, 4, 5, 6], permissions: ['file_upload'] },
            'GET /api/file/download': { roles: [1, 2, 3, 4, 5, 6], permissions: ['file_download'] },
            'DELETE /api/file/delete': { roles: [2, 3, 5], permissions: ['file_delete'] },
            'POST /api/data/export': { roles: [2, 3, 5], permissions: ['data_export'] }
        };

        Object.entries(apiPermissions).forEach(([api, config]) => {
            this.apiPermissions.set(api, config);
        });
    }

    /**
     * 加载数据权限配置
     */
    loadDataPermissions() {
        const dataPermissions = {
            // 用户数据权限
            'user_data': {
                'own_data': { roles: [1, 2, 3, 4, 5, 6], permissions: ['own_data_access'] },
                'department_data': { roles: [2, 3, 5], permissions: ['department_data_access'] },
                'all_data': { roles: [5], permissions: ['all_data_access'] }
            },

            // 群组数据权限
            'group_data': {
                'member_data': { roles: [1, 2, 3, 4, 5, 6], permissions: ['group_member_data'] },
                'manager_data': { roles: [2, 3, 5], permissions: ['group_manager_data'] },
                'admin_data': { roles: [3, 5], permissions: ['group_admin_data'] }
            },

            // 多中心数据权限
            'multicenter_data': {
                'own_exams': { roles: [1, 2, 3, 4, 5, 6], permissions: ['own_exam_data'] },
                'assigned_exams': { roles: [2, 3, 4], permissions: ['assigned_exam_data'] },
                'all_exams': { roles: [5], permissions: ['all_exam_data'] },
                'statistics_data': { roles: [2, 3, 5], permissions: ['statistics_data'] }
            }
        };

        Object.entries(dataPermissions).forEach(([dataType, permissions]) => {
            this.dataPermissions.set(dataType, permissions);
        });
    }

    /**
     * 检查功能权限
     * @param {string} feature - 功能名称
     * @param {string} action - 操作名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    hasPermission(feature, action = null, context = {}) {
        if (!this.isInitialized()) {
            console.warn('FeaturePermissionManager not initialized');
            return false;
        }

        // 如果只传入功能名，检查功能的基本访问权限
        if (!action) {
            return this.checkFeatureAccess(feature, context);
        }

        // 检查功能的特定操作权限
        return this.checkFeatureActionPermission(feature, action, context);
    }

    /**
     * 检查功能访问权限
     * @param {string} feature - 功能名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkFeatureAccess(feature, context = {}) {
        const featureConfig = this.featurePermissions.get(feature);

        if (!featureConfig) {
            return true; // 没有配置默认允许
        }

        // 检查是否有任何一个操作的权限
        for (let [action, config] of Object.entries(featureConfig)) {
            if (this.checkPermissionConfig(config, context)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查功能操作权限
     * @param {string} feature - 功能名称
     * @param {string} action - 操作名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkFeatureActionPermission(feature, action, context = {}) {
        const featureConfig = this.featurePermissions.get(feature);

        if (!featureConfig) {
            return true; // 没有配置默认允许
        }

        const actionConfig = featureConfig[action];
        if (!actionConfig) {
            return true; // 没有配置默认允许
        }

        return this.checkPermissionConfig(actionConfig, context);
    }

    /**
     * 检查API权限
     * @param {string} method - HTTP方法
     * @param {string} path - API路径
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkApiPermission(method, path, context = {}) {
        const apiKey = `${method.toUpperCase()} ${path}`;
        const apiConfig = this.apiPermissions.get(apiKey);

        if (!apiConfig) {
            return true; // 没有配置默认允许
        }

        return this.checkPermissionConfig(apiConfig, context);
    }

    /**
     * 检查数据权限
     * @param {string} dataType - 数据类型
     * @param {string} scope - 数据范围
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkDataPermission(dataType, scope, context = {}) {
        const dataConfig = this.dataPermissions.get(dataType);

        if (!dataConfig) {
            return true; // 没有配置默认允许
        }

        const scopeConfig = dataConfig[scope];
        if (!scopeConfig) {
            return true; // 没有配置默认允许
        }

        return this.checkPermissionConfig(scopeConfig, context);
    }

    /**
     * 检查权限配置
     * @param {Object} config - 权限配置
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkPermissionConfig(config, context = {}) {
        // 检查角色权限
        if (config.roles && config.roles.length > 0) {
            const userRole = this.getUserRole();
            if (!config.roles.includes(userRole)) {
                return false;
            }
        }

        // 检查特定权限
        if (config.permissions && config.permissions.length > 0) {
            return config.permissions.every(permission =>
                this.checkSpecificPermission(permission, context)
            );
        }

        return true;
    }

    /**
     * 检查特定权限
     * @param {string} permission - 权限标识
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkSpecificPermission(permission, context = {}) {
        const userId = this.getUserId();
        const userRole = this.getUserRole();

        switch (permission) {
        // 基础权限
        case 'user_create':
        case 'user_edit':
        case 'group_create':
        case 'group_edit':
        case 'exam_assign':
        case 'meeting_manage':
        case 'course_create':
        case 'course_edit':
        case 'settings_edit':
        case 'background_manage':
            return this.isAdmin();

        case 'user_delete':
        case 'group_delete':
        case 'course_delete':
        case 'user_role_change':
        case 'group_manager_set':
            return this.isSuperAdmin() || userRole === 3;

        case 'exam_review':
            return userRole === 3 || userRole === 4;

        case 'exam_judge':
            return userRole === 4;

            // 数据访问权限
        case 'own_data_access':
            return userId !== null;

        case 'department_data_access':
        case 'group_manager_data':
            return userRole >= 2;

        case 'all_data_access':
        case 'all_exam_data':
            return this.isSuperAdmin();

            // 文件权限
        case 'file_upload':
        case 'file_download':
        case 'file_share':
        case 'exam_create':
        case 'exam_edit':
        case 'meeting_create':
        case 'meeting_join':
        case 'course_view':
        case 'settings_view':
        case 'system_info_view':
            return userId !== null; // 登录用户即可

        case 'file_delete':
        case 'exam_delete':
        case 'data_export':
        case 'statistics_view':
        case 'statistics_data':
        case 'meeting_record':
        case 'live_stream':
        case 'homework_manage':
            return this.isAdmin();

        default:
            return true;
        }
    }

    /**
     * 获取用户可执行的功能操作
     * @param {string} feature - 功能名称
     * @returns {Array<string>} 可执行的操作列表
     */
    getAvailableActions(feature) {
        const featureConfig = this.featurePermissions.get(feature);

        if (!featureConfig) {
            return [];
        }

        const availableActions = [];
        for (let [action, config] of Object.entries(featureConfig)) {
            if (this.checkPermissionConfig(config)) {
                availableActions.push(action);
            }
        }

        return availableActions;
    }

    /**
     * 批量检查功能权限
     * @param {Array} features - 功能配置数组 [{feature, action, context}]
     * @returns {Object} 权限检查结果
     */
    batchCheckPermissions(features) {
        const results = {};

        features.forEach(({ feature, action, context = {}, key }) => {
            const permissionKey = key || `${feature}${action ? '_' + action : ''}`;
            results[permissionKey] = this.hasPermission(feature, action, context);
        });

        return results;
    }

    /**
     * 添加功能权限配置
     * @param {string} feature - 功能名称
     * @param {Object} permissions - 权限配置
     */
    addFeaturePermission(feature, permissions) {
        this.featurePermissions.set(feature, permissions);
    }

    /**
     * 添加API权限配置
     * @param {string} api - API标识
     * @param {Object} config - 权限配置
     */
    addApiPermission(api, config) {
        this.apiPermissions.set(api, config);
    }

    /**
     * 添加数据权限配置
     * @param {string} dataType - 数据类型
     * @param {Object} permissions - 权限配置
     */
    addDataPermission(dataType, permissions) {
        this.dataPermissions.set(dataType, permissions);
    }
}

export default FeaturePermissionManager;
