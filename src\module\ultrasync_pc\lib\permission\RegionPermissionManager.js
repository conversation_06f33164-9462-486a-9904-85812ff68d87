import BasePermissionManager from './BasePermissionManager.js';

/**
 * 区域权限管理器
 * 管理基于区域配置的功能权限（functionsStatus）
 * 这些权限在应用初始化时获取，不会因为退出登录而变化
 */
class RegionPermissionManager extends BasePermissionManager {
    constructor() {
        super();
        this.regionFunctions = {}; // 区域功能配置
        this.region = ''; // 当前区域
        this.storeWatcher = null; // store 监听器
    }

    /**
     * 初始化区域权限管理器
     * @param {Object} userInfo - 用户信息
     * @param {Object} config - 配置信息
     */
    async initialize(userInfo, config = {}) {
        this.userInfo = userInfo;
        this.config = config;
        this.initialized = true;
        await this.loadPermissions();
    }

    /**
     * 加载权限数据（实现基类的抽象方法）
     */
    async loadPermissions() {
        try {
            // 从 Vuex store 获取区域功能配置
            if (window.vm && window.vm.$store) {
                const globalParams = window.vm.$store.state.globalParams;
                this.regionFunctions = globalParams.functionsStatus || this.getDefaultRegionFunctions();
                this.region = globalParams.region || '';

                console.log('RegionPermissionManager loaded:', {
                    region: this.region,
                    functions: this.regionFunctions,
                    isDefault: !globalParams.functionsStatus || Object.keys(globalParams.functionsStatus).length === 0
                });

                // 监听 functionsStatus 的变化，当从服务器获取到数据时自动更新
                this.setupStoreWatcher();
            } else {
                console.warn('Vue store not available, using default region functions');
                this.regionFunctions = this.getDefaultRegionFunctions();
            }
        } catch (error) {
            console.error('Failed to load region permissions:', error);
            // 使用默认配置
            this.regionFunctions = this.getDefaultRegionFunctions();
        }
    }

    /**
     * 设置 store 监听器，当 functionsStatus 更新时自动重新加载
     */
    setupStoreWatcher() {
        if (window.vm && window.vm.$store && !this.storeWatcher) {
            // 监听 globalParams.functionsStatus 的变化
            this.storeWatcher = window.vm.$store.watch(
                (state) => state.globalParams.functionsStatus,
                (newFunctionsStatus, oldFunctionsStatus) => {
                    if (newFunctionsStatus && newFunctionsStatus !== oldFunctionsStatus) {
                        console.log('RegionPermissionManager: functionsStatus updated from server', newFunctionsStatus);
                        this.regionFunctions = { ...newFunctionsStatus };
                        this.clearCache(); // 清除缓存以确保权限检查使用新数据
                    }
                },
                { deep: true }
            );
        }
    }

    /**
     * 获取区域功能权限映射关系
     * 定义区域功能与具体权限操作的映射关系
     * @returns {Object} 权限映射配置
     */
    getRegionPermissionMapping() {
        return {
            // 直播功能映射
            live: [
                'liveStart',           // 开始直播
                'liveJoin',            // 加入直播
                'liveManagement',      // 直播管理
                'liveInvite',          // 直播邀请
                'liveRecord',          // 直播录制
            ],

            // 电视墙功能映射
            tvwall: [
                'tvWallPlay',          // 电视墙播放
                'tvWallPush',          // 电视墙推送
                'tvWallManage',        // 电视墙管理
                'tvWallControl',       // 电视墙控制
                'webTvwallEnterConversation', // web电视墙进入会话
            ],

            // AI功能映射
            ai: [
                'aiAnalyze',           // AI分析
                'aiReport',            // AI报告
                'aiAssistant',         // AI助手
            ],

            // 乳腺AI功能映射
            breastAI: [
                'breastAIAnalyze',     // 乳腺AI分析
                'breastAIReport',      // 乳腺AI报告
                'breastAIAssistant',   // 乳腺AI助手
            ],

            // DR助手功能映射
            drAIAssistant: [
                'drAIAnalyze',         // DR AI分析
                'drAIReport',          // DR AI报告
                'drAIAssistant',       // DR AI助手
            ],

            // 产科AI功能映射
            obstetricalAI: [
                'obstetricalAIAnalyze', // 产科AI分析
                'obstetricalAIReport',  // 产科AI报告
                'obstetricalAIAssistant', // 产科AI助手
            ],

            // 云端统计功能映射
            cloudStatistic: [
                'cloudStatisticView',   // 查看云端统计
                'cloudStatisticExport', // 导出云端统计
                'cloudStatisticManage', // 管理云端统计
            ],

            // BI统计功能映射
            qcStatistics: [
                'qcStatisticsView',     // 查看BI统计
                'qcStatisticsExport',   // 导出BI统计
                'qcStatisticsManage',   // 管理BI统计
            ],

            // 乳腺病例库功能映射
            breastCases: [
                'breastCasesView',      // 查看乳腺病例
                'breastCasesAdd',       // 添加乳腺病例
                'breastCasesEdit',      // 编辑乳腺病例
                'breastCasesDelete',    // 删除乳腺病例
                'breastCasesExport',    // 导出乳腺病例
            ],

            // 图书馆功能映射
            library: [
                'libraryView',          // 查看图书馆
                'libraryUpload',        // 上传到图书馆
                'libraryDownload',      // 从图书馆下载
                'libraryManage',        // 管理图书馆
            ],

            // 群落功能映射
            groupset: [
                'groupsetCreate',       // 创建群落
                'groupsetJoin',         // 加入群落
                'groupsetManage',       // 管理群落
                'groupsetInvite',       // 邀请加入群落
            ],

            // 微信功能映射
            wechat: [
                'wechatShare',          // 微信分享
                'wechatLogin',          // 微信登录
                'wechatNotify',         // 微信通知
            ],

            // 推荐码功能映射
            referralCode: [
                'referralCodeGenerate', // 生成推荐码
                'referralCodeUse',      // 使用推荐码
                'referralCodeManage',   // 管理推荐码
            ],

            // Web推桌面功能映射
            webShareScreen: [
                'webShareScreenStart', // 开始推桌面
                'webShareScreenJoin',  // 加入推桌面
                'webShareScreenControl', // 控制推桌面
            ],

            // 智能教培功能映射
            smartEdTechTraining: [
                'trainingView',         // 查看培训
                'trainingCreate',       // 创建培训
                'trainingManage',       // 管理培训
                'trainingParticipate',  // 参与培训
            ],

            // 超声质控报告功能映射
            ultrasoundQCReport: [
                'qcReportView',         // 查看质控报告
                'qcReportGenerate',     // 生成质控报告
                'qcReportExport',       // 导出质控报告
                'qcReportManage',       // 管理质控报告
            ],

            // Mindray Club功能映射
            club: [
                'clubJoin',             // 加入俱乐部
                'clubParticipate',      // 参与俱乐部活动
                'clubManage',           // 管理俱乐部
            ],

            // 职业身份强制设置功能映射
            professionalIdentityForce: [
                'professionalIdentitySet',    // 设置职业身份
                'professionalIdentityVerify', // 验证职业身份
                'professionalIdentityManage', // 管理职业身份
            ],
        };
    }

    /**
     * 获取默认区域功能配置
     * @returns {Object} 默认功能配置
     */
    getDefaultRegionFunctions() {
        return {
            live: 1, // 直播
            library: 1, // 图书馆
            cloudStatistic: 1, // 云端统计
            breastCases: 1, // 乳腺病例库
            breastAI: 1, // 小麦同学
            drAIAssistant: 1, // dr助手
            groupset: 1, // 群落
            wechat: 1, // 微信功能
            obstetricalAI: 1, // 产科AI
            tvwall: 1, // 电视墙
            qcStatistics: 1, // bi统计
            referralCode: 1, // 推荐码
            webShareScreen: 1, // web推桌面
            webTvwallEnterConversation: 1, // web电视墙进入会话
            ai: 1, // AI应用
            smartEdTechTraining: 1, // 智能教培
            ultrasoundQCReport: 1, // 超声质控报告
            club: 1, // Mindray Club
            professionalIdentityForce: 0, // 职业身份强制设置
        };
    }

    /**
     * 检查区域功能权限
     * @param {string} functionName - 功能名称或映射的具体权限名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    hasPermission(functionName, context = {}) {
        if (!this.initialized) {
            console.warn('RegionPermissionManager not initialized');
            return false;
        }

        // 首先检查是否是直接的区域功能
        if (functionName in this.regionFunctions) {
            const hasPermission = Boolean(this.regionFunctions[functionName]);
            this.logPermissionCheck(`region:${functionName}`, hasPermission, context);
            return hasPermission;
        }

        // 检查是否是映射的具体权限
        const mappedRegionFunction = this.findRegionFunctionByMappedPermission(functionName);
        if (mappedRegionFunction) {
            const hasPermission = Boolean(this.regionFunctions[mappedRegionFunction]);
            this.logPermissionCheck(`region:${mappedRegionFunction}->${functionName}`, hasPermission, context);
            return hasPermission;
        }

        // 如果都不匹配，记录警告并返回 false
        console.warn(`Unknown region function or mapped permission: ${functionName}`);
        return false;
    }

    /**
     * 根据映射的权限名称查找对应的区域功能
     * @param {string} mappedPermission - 映射的权限名称
     * @returns {string|null} 对应的区域功能名称，如果未找到返回 null
     */
    findRegionFunctionByMappedPermission(mappedPermission) {
        const permissionMapping = this.getRegionPermissionMapping();

        for (const [regionFunction, mappedPermissions] of Object.entries(permissionMapping)) {
            if (mappedPermissions.includes(mappedPermission)) {
                return regionFunction;
            }
        }

        return null;
    }

    /**
     * 检查功能是否启用
     * @param {string} functionName - 功能名称
     * @returns {boolean} 是否启用
     */
    isFunctionEnabled(functionName) {
        return this.hasPermission(functionName);
    }

    /**
     * 获取所有启用的功能列表
     * @returns {Array<string>} 启用的功能名称列表
     */
    getEnabledFunctions() {
        const enabledFunctions = [];
        for (const [functionName, enabled] of Object.entries(this.regionFunctions)) {
            if (enabled) {
                enabledFunctions.push(functionName);
            }
        }
        return enabledFunctions;
    }

    /**
     * 获取所有禁用的功能列表
     * @returns {Array<string>} 禁用的功能名称列表
     */
    getDisabledFunctions() {
        const disabledFunctions = [];
        for (const [functionName, enabled] of Object.entries(this.regionFunctions)) {
            if (!enabled) {
                disabledFunctions.push(functionName);
            }
        }
        return disabledFunctions;
    }

    /**
     * 批量检查功能权限
     * @param {Array<string>} functionNames - 功能名称列表
     * @returns {Object} 权限检查结果
     */
    batchCheckPermissions(functionNames) {
        const results = {};
        functionNames.forEach(functionName => {
            results[functionName] = this.hasPermission(functionName);
        });
        return results;
    }

    /**
     * 获取指定区域功能的所有映射权限
     * @param {string} regionFunction - 区域功能名称
     * @returns {Array<string>} 映射的权限列表
     */
    getMappedPermissions(regionFunction) {
        const permissionMapping = this.getRegionPermissionMapping();
        return permissionMapping[regionFunction] || [];
    }

    /**
     * 检查指定区域功能的所有映射权限
     * @param {string} regionFunction - 区域功能名称
     * @returns {Object} 映射权限的检查结果
     */
    checkAllMappedPermissions(regionFunction) {
        const mappedPermissions = this.getMappedPermissions(regionFunction);
        const results = {};

        const regionFunctionEnabled = Boolean(this.regionFunctions[regionFunction]);

        mappedPermissions.forEach(permission => {
            results[permission] = regionFunctionEnabled;
        });

        return results;
    }

    /**
     * 获取所有启用的映射权限
     * @returns {Array<string>} 所有启用的映射权限列表
     */
    getAllEnabledMappedPermissions() {
        const enabledPermissions = [];
        const permissionMapping = this.getRegionPermissionMapping();

        for (const [regionFunction, mappedPermissions] of Object.entries(permissionMapping)) {
            if (this.regionFunctions[regionFunction]) {
                enabledPermissions.push(...mappedPermissions);
            }
        }

        return enabledPermissions;
    }

    /**
     * 获取权限映射关系摘要
     * @returns {Object} 权限映射摘要
     */
    getPermissionMappingSummary() {
        const permissionMapping = this.getRegionPermissionMapping();
        const summary = {};

        for (const [regionFunction, mappedPermissions] of Object.entries(permissionMapping)) {
            const isEnabled = Boolean(this.regionFunctions[regionFunction]);
            summary[regionFunction] = {
                enabled: isEnabled,
                mappedPermissions: mappedPermissions,
                enabledMappedPermissions: isEnabled ? mappedPermissions : []
            };
        }

        return summary;
    }

    /**
     * 更新区域功能配置
     * @param {Object} newFunctions - 新的功能配置
     */
    updateRegionFunctions(newFunctions) {
        this.regionFunctions = { ...this.regionFunctions, ...newFunctions };

        // 同步更新到 Vuex store
        if (window.vm && window.vm.$store) {
            window.vm.$store.commit('globalParams/updateFunctionsStatus', newFunctions);
        }

        // 清除缓存
        this.clearCache();

        console.log('Region functions updated:', newFunctions);
    }

    /**
     * 获取当前区域
     * @returns {string} 当前区域
     */
    getCurrentRegion() {
        return this.region;
    }

    /**
     * 获取区域功能配置
     * @returns {Object} 区域功能配置
     */
    getRegionFunctions() {
        return { ...this.regionFunctions };
    }

    /**
     * 检查是否为CE区域
     * @returns {boolean} 是否为CE区域
     */
    isCERegion() {
        if (window.vm && window.vm.$store) {
            return window.vm.$store.state.globalParams.isCE || false;
        }
        return false;
    }

    /**
     * 检查功能在当前区域是否可用
     * @param {string} functionName - 功能名称
     * @param {Object} options - 选项
     * @param {boolean} options.checkCE - 是否检查CE区域限制
     * @returns {boolean} 是否可用
     */
    isFunctionAvailable(functionName, options = {}) {
        const { checkCE = false } = options;

        // 基础权限检查
        if (!this.hasPermission(functionName)) {
            return false;
        }

        // CE区域特殊检查
        if (checkCE && this.isCERegion()) {
            // 某些功能在CE区域不可用
            const ceRestrictedFunctions = ['breastCases', 'cloudStatistic'];
            if (ceRestrictedFunctions.includes(functionName)) {
                return false;
            }
        }

        return true;
    }

    /**
     * 获取权限错误信息
     * @param {string} functionName - 功能名称
     * @returns {string} 错误信息
     */
    getPermissionErrorMessage(functionName) {
        if (!(functionName in this.regionFunctions)) {
            return `未知的功能: ${functionName}`;
        }

        if (!this.regionFunctions[functionName]) {
            return `当前区域不支持此功能: ${functionName}`;
        }

        return super.getPermissionErrorMessage(functionName);
    }

    /**
     * 销毁区域权限管理器
     */
    destroy() {
        // 清理 store 监听器
        if (this.storeWatcher) {
            this.storeWatcher();
            this.storeWatcher = null;
        }

        this.regionFunctions = {};
        this.region = '';
        super.destroy();
    }
}

export default RegionPermissionManager;
