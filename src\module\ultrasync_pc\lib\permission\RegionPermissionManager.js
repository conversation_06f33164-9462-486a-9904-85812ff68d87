import BasePermissionManager from './BasePermissionManager.js';

/**
 * 区域权限管理器
 * 管理基于区域配置的功能权限（functionsStatus）
 * 这些权限在应用初始化时获取，不会因为退出登录而变化
 */
class RegionPermissionManager extends BasePermissionManager {
    constructor() {
        super();
        this.regionFunctions = {}; // 区域功能配置
        this.region = ''; // 当前区域
    }

    /**
     * 初始化区域权限管理器
     * @param {Object} userInfo - 用户信息
     * @param {Object} config - 配置信息
     */
    async initialize(userInfo, config = {}) {
        await super.initialize(userInfo, config);
        await this.loadRegionPermissions();
    }

    /**
     * 加载区域权限数据
     */
    async loadRegionPermissions() {
        try {
            // 从 Vuex store 获取区域功能配置
            if (window.vm && window.vm.$store) {
                const globalParams = window.vm.$store.state.globalParams;
                this.regionFunctions = globalParams.functionsStatus || {};
                this.region = globalParams.region || '';
                
                console.log('RegionPermissionManager loaded:', {
                    region: this.region,
                    functions: this.regionFunctions
                });
            }
        } catch (error) {
            console.error('Failed to load region permissions:', error);
            // 使用默认配置
            this.regionFunctions = this.getDefaultRegionFunctions();
        }
    }

    /**
     * 获取默认区域功能配置
     * @returns {Object} 默认功能配置
     */
    getDefaultRegionFunctions() {
        return {
            live: 1, // 直播
            library: 1, // 图书馆
            cloudStatistic: 1, // 云端统计
            breastCases: 1, // 乳腺病例库
            breastAI: 1, // 小麦同学
            drAIAssistant: 1, // dr助手
            groupset: 1, // 群落
            wechat: 1, // 微信功能
            obstetricalAI: 1, // 产科AI
            tvwall: 1, // 电视墙
            qcStatistics: 1, // bi统计
            referralCode: 1, // 推荐码
            webShareScreen: 1, // web推桌面
            webTvwallEnterConversation: 1, // web电视墙进入会话
            ai: 1, // AI应用
            smartEdTechTraining: 1, // 智能教培
            ultrasoundQCReport: 1, // 超声质控报告
            club: 1, // Mindray Club
            professionalIdentityForce: 0, // 职业身份强制设置
        };
    }

    /**
     * 检查区域功能权限
     * @param {string} functionName - 功能名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    hasPermission(functionName, context = {}) {
        if (!this.initialized) {
            console.warn('RegionPermissionManager not initialized');
            return false;
        }

        // 检查功能是否存在
        if (!(functionName in this.regionFunctions)) {
            console.warn(`Unknown region function: ${functionName}`);
            return false;
        }

        const hasPermission = Boolean(this.regionFunctions[functionName]);
        
        // 记录权限检查日志
        this.logPermissionCheck(`region:${functionName}`, hasPermission, context);
        
        return hasPermission;
    }

    /**
     * 检查功能是否启用
     * @param {string} functionName - 功能名称
     * @returns {boolean} 是否启用
     */
    isFunctionEnabled(functionName) {
        return this.hasPermission(functionName);
    }

    /**
     * 获取所有启用的功能列表
     * @returns {Array<string>} 启用的功能名称列表
     */
    getEnabledFunctions() {
        const enabledFunctions = [];
        for (const [functionName, enabled] of Object.entries(this.regionFunctions)) {
            if (enabled) {
                enabledFunctions.push(functionName);
            }
        }
        return enabledFunctions;
    }

    /**
     * 获取所有禁用的功能列表
     * @returns {Array<string>} 禁用的功能名称列表
     */
    getDisabledFunctions() {
        const disabledFunctions = [];
        for (const [functionName, enabled] of Object.entries(this.regionFunctions)) {
            if (!enabled) {
                disabledFunctions.push(functionName);
            }
        }
        return disabledFunctions;
    }

    /**
     * 批量检查功能权限
     * @param {Array<string>} functionNames - 功能名称列表
     * @returns {Object} 权限检查结果
     */
    batchCheckPermissions(functionNames) {
        const results = {};
        functionNames.forEach(functionName => {
            results[functionName] = this.hasPermission(functionName);
        });
        return results;
    }

    /**
     * 更新区域功能配置
     * @param {Object} newFunctions - 新的功能配置
     */
    updateRegionFunctions(newFunctions) {
        this.regionFunctions = { ...this.regionFunctions, ...newFunctions };
        
        // 同步更新到 Vuex store
        if (window.vm && window.vm.$store) {
            window.vm.$store.commit('globalParams/updateFunctionsStatus', newFunctions);
        }
        
        // 清除缓存
        this.clearCache();
        
        console.log('Region functions updated:', newFunctions);
    }

    /**
     * 获取当前区域
     * @returns {string} 当前区域
     */
    getCurrentRegion() {
        return this.region;
    }

    /**
     * 获取区域功能配置
     * @returns {Object} 区域功能配置
     */
    getRegionFunctions() {
        return { ...this.regionFunctions };
    }

    /**
     * 检查是否为CE区域
     * @returns {boolean} 是否为CE区域
     */
    isCERegion() {
        if (window.vm && window.vm.$store) {
            return window.vm.$store.state.globalParams.isCE || false;
        }
        return false;
    }

    /**
     * 检查功能在当前区域是否可用
     * @param {string} functionName - 功能名称
     * @param {Object} options - 选项
     * @param {boolean} options.checkCE - 是否检查CE区域限制
     * @returns {boolean} 是否可用
     */
    isFunctionAvailable(functionName, options = {}) {
        const { checkCE = false } = options;
        
        // 基础权限检查
        if (!this.hasPermission(functionName)) {
            return false;
        }
        
        // CE区域特殊检查
        if (checkCE && this.isCERegion()) {
            // 某些功能在CE区域不可用
            const ceRestrictedFunctions = ['breastCases', 'cloudStatistic'];
            if (ceRestrictedFunctions.includes(functionName)) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * 获取权限错误信息
     * @param {string} functionName - 功能名称
     * @returns {string} 错误信息
     */
    getPermissionErrorMessage(functionName) {
        if (!(functionName in this.regionFunctions)) {
            return `未知的功能: ${functionName}`;
        }
        
        if (!this.regionFunctions[functionName]) {
            return `当前区域不支持此功能: ${functionName}`;
        }
        
        return super.getPermissionErrorMessage(functionName);
    }

    /**
     * 销毁区域权限管理器
     */
    destroy() {
        this.regionFunctions = {};
        this.region = '';
        super.destroy();
    }
}

export default RegionPermissionManager;
