export const CHAT_TYPE = {
    CHAT_WINDOW: 1, //聊天窗口
    GALLERY: 2,//画廊
    CHAT_HISTORY: 3,//聊天历史记录
    CONFERENCE:4,//直播
};
export const EMOJI_LIST = [
    '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '😤',
    '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚',
    '😋', '😛', '😜', '🤪', '😝', '🤑', '🤗', '🤭', '🤫', '🤔',
    '🤐', '🤨', '😐', '😑', '😶', '😏', '😒', '🙄', '😬', '🤥',
    '😌', '😔', '😪', '🤤', '😴', '😷', '🤒', '🤕', '🤢', '🤮',
    '🤧', '😵', '🤯', '🤠', '😎', '🤓', '🧐', '😕', '😟', '🙁',
    '😮', '😯', '😲', '😳', '🥺', '😦', '😧', '😨', '😰', '😥',
    '😢', '😭', '😱', '😖', '😣', '😞', '😓', '😩', '😫', '🥱',
    '❤️', '😡', '😠', '🤬', '🤯', '😈', '👿', '💀', '☠️', '💩',
    '🤡', '👹', '👺', '👻', '👽', '👾', '🤖', '😺', '😸', '😹',
    '😻', '😼', '😽', '🙀', '😿', '😾', '🙈', '🙉', '🙊', '💋',
]

export const CLOUD_TEST_TYPE = Object.freeze({
    VIEW: 1,         // 查看试卷
    ANSWER: 2,       // 答卷
    CORRECT: 3,      // 批卷
    VIEW_RESULT: 4,  // 查看结果
    EDIT: 5,         // 编辑试卷
});

export const BODY_PART = Object.freeze({
    SUPERFICIAL: 'superficial',         // 浅表介入
    ABDOMEN: 'abdomen',       // 腹部
    CARDIO: 'cardiac',      // 心血管
    GYN: 'obgyn',  // '妇产'
});

export const SMART_TECH_TRAINING_TEST_TYPE = Object.freeze({
    ATTACHMENT_UPLOAD: 1, // 附件上传
    ONLINE_QUIZ: 2,       // 在线答题
});
export const SMART_TECH_TRAINING_ROLE = Object.freeze({
    STUDENT: 'student', // 学生
    PI_TEACHER: 'PI', // 导师
    SUPERVISOR: 'supervisor', // 督导
});
export const SEX = Object.freeze({
    MALE: 0, // 男
    FEMALE: 1, // 女
    UNKNOWN: 2, // 未知
    0:'MALE',
    1:'FEMALE',
    2:'UNKNOWN',
});
export const SMART_TECH_TRAINING_TEST_RETRY_TYPE = Object.freeze({
    //多次作答，单次作答
    MULTIPLE: 2, // 多次作答
    SINGLE: 1, // 单次作答
});
export const SMART_TECH_TRAINING_STUDENT_STATUS = Object.freeze({
    //0申请中 1通过 2拒绝 3禁用
    APPLYING: 0,
    PASSED: 1,
    REJECTED: 2,
    DISABLED: 3,
    0:'APPLYING',
    1:'PASSED',
    2:'REJECTED',
    3:'DISABLED',
});
//新增已批改未批改状态 0未提交 1未批改 2已批改
export const SMART_TECH_TRAINING_TEST_CORRECT_STATUS = Object.freeze({
    UNSUBMITTED: 0,
    UNCORRECTED: 1,
    CORRECTED: 2,
    0:'UNSUBMITTED',
    1:'UNCORRECTED',
    2:'CORRECTED',
});

//考核通过状态
export const SMART_TECH_TRAINING_TEST_PASS_STATUS = Object.freeze({
    NOT_PASS: false, // 未通过
    PASS: true, // 通过
});

//新增培训最终状态 0未完成 1待审核 2已通过 3不通过
export const SMART_TECH_TRAINING_FINAL_STATUS = Object.freeze({
    // UNCOMPLETED: 0,
    // PENDING: 1,
    PASSED: 2,
    FAILED: 3,
    // 0: 'UNCOMPLETED',
    // 1: 'PENDING',
    2: 'PASSED',
    3: 'FAILED',
});

// 应用领域枚举
export const APPLICATION_AREA = Object.freeze({
    MEDICAL_INSTITUTION: 1,                 // 医疗机构
    VENDOR_PERSONNEL: 2,                    // 厂商人员
    MEDICAL_SCHOOL: 3,                      // 医学院校
    OTHER: 4,                              // 其他
    1: 'MEDICAL_INSTITUTION',
    2: 'VENDOR_PERSONNEL',
    3: 'MEDICAL_SCHOOL',
    4: 'OTHER',
});

// 职业身份枚举
export const PROFESSIONAL_IDENTITY = Object.freeze({
    PHYSICIAN: 1,                           // 医生
    SONOGRAPHER: 2,                         // 超声技师/超声技术员
    ADMINISTRATOR: 3,                       // 管理员
    RESEARCHER: 4,                          // 研究员
    TECHNICAL_SUPPORT: 5,                   // 技术支持
    OTHER_VENDOR: 6,                        // 厂商其他人员
    OTHER_MEDICAL: 7,                       // 其他医疗领域相关用户
    TEACHER: 8,                            // 老师
    STUDENT: 9,                            // 学生
    OTHER_MEDICAL_SCHOOL: 10,              // 其他医学院人员
    1: 'PHYSICIAN',
    2: 'SONOGRAPHER',
    3: 'ADMINISTRATOR',
    4: 'RESEARCHER',
    5: 'TECHNICAL_SUPPORT',
    6: 'OTHER_VENDOR',
    7: 'OTHER_MEDICAL',
    8: 'TEACHER',
    9: 'STUDENT',
    10: 'OTHER_MEDICAL_SCHOOL',
});

// 根据应用领域获取对应的职业身份选项
export const PROFESSIONAL_IDENTITY_BY_APPLICATION_AREA = Object.freeze({
    [APPLICATION_AREA.MEDICAL_INSTITUTION]: [
        PROFESSIONAL_IDENTITY.PHYSICIAN,
        PROFESSIONAL_IDENTITY.SONOGRAPHER,
        PROFESSIONAL_IDENTITY.RESEARCHER,
        PROFESSIONAL_IDENTITY.OTHER_MEDICAL
    ],
    [APPLICATION_AREA.VENDOR_PERSONNEL]: [
        PROFESSIONAL_IDENTITY.ADMINISTRATOR,
        PROFESSIONAL_IDENTITY.TECHNICAL_SUPPORT,
        PROFESSIONAL_IDENTITY.OTHER_VENDOR
    ],
    [APPLICATION_AREA.MEDICAL_SCHOOL]: [
        PROFESSIONAL_IDENTITY.TEACHER,
        PROFESSIONAL_IDENTITY.STUDENT,
        PROFESSIONAL_IDENTITY.OTHER_MEDICAL_SCHOOL
    ],
    [APPLICATION_AREA.OTHER]: [] // 其他情况下不显示任何选项
});

// 用户角色常量
export const USER_ROLE = Object.freeze({
    TEMP_USER: 0,        // 临时用户
    NORMAL_USER: 1,      // 普通用户
    ADMIN: 2,            // 管理员
    SUPER_ADMIN: 3,      // 超级管理员
    DIRECTOR: 4,         // 主任
    0: 'TEMP_USER',
    1: 'NORMAL_USER',
    2: 'ADMIN',
    3: 'SUPER_ADMIN',
    4: 'DIRECTOR'
});
