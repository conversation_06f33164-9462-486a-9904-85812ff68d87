/**
 * 权限管理器使用示例
 * 展示如何在业务代码中使用权限检查
 */

import permissionManager from './index.js';

/**
 * 示例1：在Vue组件中使用权限检查
 */
export const vueComponentExample = {
    mounted() {
        // 检查后台管理权限
        if (permissionManager.checkPermission('backgroundManage')) {
            console.log('用户有后台管理权限');
            this.showAdminPanel = true;
        }

        // 检查用户管理权限
        if (permissionManager.checkPermission('userManage')) {
            console.log('用户有用户管理权限');
            this.showUserManagement = true;
        }

        // 检查是否为超级管理员
        if (permissionManager.checkPermission('super_admin')) {
            console.log('用户是超级管理员');
            this.showSuperAdminFeatures = true;
        }
    },

    methods: {
        // 在方法中检查权限
        deleteUser(userId) {
            if (!permissionManager.checkPermission('user_delete')) {
                this.$message.error('您没有删除用户的权限');
                return;
            }

            // 执行删除操作
            this.performDeleteUser(userId);
        },

        // 使用Vue混入的方法
        editUser(userId) {
            if (!this.$checkPermission('user_edit')) {
                this.$message.error('您没有编辑用户的权限');
                return;
            }

            // 执行编辑操作
            this.performEditUser(userId);
        }
    }
};

/**
 * 示例2：在路由守卫中使用权限检查
 */
export const routeGuardExample = {
    beforeRouteEnter(to, from, next) {
        // 检查路由权限
        if (permissionManager.checkPermission('backgroundManage')) {
            next();
        } else {
            next('/unauthorized');
        }
    }
};

/**
 * 示例3：在API调用前检查权限
 */
export const apiPermissionExample = {
    async createUser(userData) {
        // 在调用API前检查权限
        if (!permissionManager.checkPermission('user_create')) {
            throw new Error('没有创建用户的权限');
        }

        // 调用API
        return await this.$http.post('/api/users', userData);
    },

    async deleteUser(userId) {
        // 检查删除权限
        if (!permissionManager.checkPermission('user_delete')) {
            throw new Error('没有删除用户的权限');
        }

        return await this.$http.delete(`/api/users/${userId}`);
    }
};

/**
 * 示例4：批量权限检查
 */
export const batchPermissionExample = {
    mounted() {
        // 批量检查多个权限
        const permissions = permissionManager.batchCheckPermissions({
            features: [
                { feature: 'backgroundManage', key: 'canAccessAdmin' },
                { feature: 'userManage', key: 'canManageUsers' },
                { feature: 'hospitalManage', key: 'canManageHospitals' }
            ]
        });

        // 根据权限结果设置UI状态
        this.canAccessAdmin = permissions.canAccessAdmin;
        this.canManageUsers = permissions.canManageUsers;
        this.canManageHospitals = permissions.canManageHospitals;
    }
};

/**
 * 示例5：在模板中使用权限指令
 */
export const templateDirectiveExample = `
<template>
    <div>
        <!-- 使用v-permission指令控制元素显示 -->
        <el-button
            v-permission.feature="'user_create'"
            @click="createUser">
            创建用户
        </el-button>

        <!-- 使用v-permission指令禁用元素 -->
        <el-button
            v-permission.feature.disable="'user_delete'"
            @click="deleteUser">
            删除用户
        </el-button>

        <!-- 使用方法检查权限 -->
        <el-button
            v-if="$checkPermission('user_edit')"
            @click="editUser">
            编辑用户
        </el-button>

        <!-- 管理员专用功能 -->
        <div v-if="$checkPermission('admin')">
            <h3>管理员功能</h3>
            <el-button @click="manageSystem">系统管理</el-button>
        </div>

        <!-- 超级管理员专用功能 -->
        <div v-if="$checkPermission('super_admin')">
            <h3>超级管理员功能</h3>
            <el-button @click="manageSuperAdmin">超级管理</el-button>
        </div>
    </div>
</template>
`;

/**
 * 示例6：动态权限检查
 */
export const dynamicPermissionExample = {
    data() {
        return {
            userActions: [
                { name: 'create', label: '创建', permission: 'user_create' },
                { name: 'edit', label: '编辑', permission: 'user_edit' },
                { name: 'delete', label: '删除', permission: 'user_delete' },
                { name: 'export', label: '导出', permission: 'data_export' }
            ]
        };
    },

    computed: {
        // 计算用户可执行的操作
        availableActions() {
            return this.userActions.filter(action =>
                permissionManager.checkPermission(action.permission)
            );
        }
    },

    methods: {
        // 动态执行操作
        executeAction(actionName) {
            const action = this.userActions.find(a => a.name === actionName);
            if (!action) {
                return;
            }

            if (!permissionManager.checkPermission(action.permission)) {
                this.$message.error(`您没有${action.label}的权限`);
                return;
            }

            // 执行对应的操作
            this[`perform${actionName.charAt(0).toUpperCase() + actionName.slice(1)}`]();
        }
    }
};

/**
 * 示例7：权限配置扩展
 */
export const permissionConfigExample = {
    // 添加自定义权限检查逻辑
    setupCustomPermissions() {
        // 可以通过扩展权限管理器来添加自定义权限逻辑
        const originalCheckPermission = permissionManager.checkPermission;

        permissionManager.checkPermission = function(permission, context = {}) {
            // 自定义权限逻辑
            if (permission === 'customFeature') {
                return this.getUserRole() >= 2 && context.hospitalId === this.getUserInfo().hospitalId;
            }

            // 调用原始方法
            return originalCheckPermission.call(this, permission, context);
        };
    }
};

/**
 * 常用权限标识说明
 */
export const permissionIdentifiers = {
    // 后台管理
    backgroundManage: '后台管理页面访问权限',

    // 用户管理
    userManage: '用户管理模块权限',
    user_create: '创建用户权限',
    user_edit: '编辑用户权限',
    user_delete: '删除用户权限（超级管理员）',
    user_role_change: '修改用户角色权限（超级管理员）',

    // 组织管理
    groupManage: '组织管理模块权限',
    group_create: '创建组织权限',
    group_edit: '编辑组织权限',
    group_delete: '删除组织权限（超级管理员）',

    // 医院管理
    hospitalManage: '医院管理模块权限',
    hospital_create: '创建医院权限',
    hospital_edit: '编辑医院权限',
    hospital_delete: '删除医院权限（超级管理员）',

    // 考试管理
    examManage: '考试管理模块权限',
    exam_assign: '分配考试权限',
    exam_review: '审核考试权限（主任及以上）',

    // 系统设置
    settingsManage: '系统设置权限',
    settings_edit: '编辑系统设置权限',

    // 数据操作
    dataExport: '数据导出权限',
    batchOperation: '批量操作权限',

    // 角色权限
    admin: '管理员权限',
    super_admin: '超级管理员权限',
    manager: '管理者权限（角色2及以上）',
    director: '主任权限（角色3及以上）',
    judge: '评委权限（角色4）',
    user: '普通用户权限',

    // 区域功能权限
    live: '直播功能权限',
    library: '图书馆功能权限',
    cloudStatistic: '云端统计功能权限',
    breastCases: '乳腺病例库功能权限',
    breastAI: '小麦同学AI功能权限',
    drAIAssistant: 'DR助手功能权限',
    groupset: '群落功能权限',
    wechat: '微信功能权限',
    obstetricalAI: '产科AI功能权限',
    tvwall: '电视墙功能权限',
    qcStatistics: 'BI统计功能权限',
    referralCode: '推荐码功能权限',
    webShareScreen: 'Web推桌面功能权限',
    webTvwallEnterConversation: 'Web电视墙进入会话功能权限',
    ai: 'AI应用功能权限',
    smartEdTechTraining: '智能教培功能权限',
    ultrasoundQCReport: '超声质控报告功能权限',
    club: 'Mindray Club功能权限',
    professionalIdentityForce: '职业身份强制设置功能权限'
};

/**
 * 示例8：区域功能权限使用示例
 */
export const regionPermissionExample = {
    template: `
        <div>
            <!-- 使用指令控制区域功能显示 -->
            <button v-permission.region="'live'" @click="startLive">开始直播</button>
            <div v-permission.region="'tvwall'" class="tvwall-section">
                <h3>电视墙功能</h3>
                <button @click="openTvWall">打开电视墙</button>
            </div>

            <!-- 使用计算属性控制 -->
            <div v-if="canUseAI">
                <h3>AI功能</h3>
                <button v-if="canUseBreastAI" @click="openBreastAI">小麦同学</button>
                <button v-if="canUseDrAI" @click="openDrAI">DR助手</button>
                <button v-if="canUseObstetricalAI" @click="openObstetricalAI">产科AI</button>
            </div>

            <!-- 统计功能 -->
            <div v-if="canUseStatistics">
                <h3>统计功能</h3>
                <button v-if="canUseCloudStatistic" @click="openCloudStatistic">云端统计</button>
                <button v-if="canUseQcStatistics" @click="openQcStatistics">BI统计</button>
            </div>

            <!-- 根据区域显示不同功能 -->
            <div v-if="!isCERegion">
                <button v-permission.region="'breastCases'">乳腺病例库</button>
                <button v-permission.region="'cloudStatistic'">云端统计</button>
            </div>
        </div>
    `,

    computed: {
        // 检查AI相关功能
        canUseAI() {
            return this.$isRegionFunctionEnabled('ai');
        },

        canUseBreastAI() {
            return this.$isRegionFunctionAvailable('breastAI', { checkCE: true });
        },

        canUseDrAI() {
            return this.$isRegionFunctionEnabled('drAIAssistant');
        },

        canUseObstetricalAI() {
            return this.$isRegionFunctionEnabled('obstetricalAI');
        },

        // 检查统计功能
        canUseStatistics() {
            return this.canUseCloudStatistic || this.canUseQcStatistics;
        },

        canUseCloudStatistic() {
            return this.$isRegionFunctionAvailable('cloudStatistic', { checkCE: true });
        },

        canUseQcStatistics() {
            return this.$isRegionFunctionEnabled('qcStatistics');
        },

        // 检查是否为CE区域
        isCERegion() {
            return this.$store.state.globalParams.isCE;
        },

        // 获取当前区域
        currentRegion() {
            return this.$getCurrentRegion();
        }
    },

    methods: {
        startLive() {
            if (!this.$checkRegionFunction('live')) {
                this.$message.error('当前区域不支持直播功能');
                return;
            }
            // 执行直播逻辑
            console.log('开始直播');
        },

        openTvWall() {
            if (!this.$checkRegionFunction('tvwall')) {
                this.$message.error('当前区域不支持电视墙功能');
                return;
            }
            // 执行电视墙逻辑
            console.log('打开电视墙');
        },

        openBreastAI() {
            if (!this.$isRegionFunctionAvailable('breastAI', { checkCE: true })) {
                this.$message.error('当前区域不支持乳腺AI功能');
                return;
            }
            // 执行乳腺AI逻辑
            console.log('打开小麦同学');
        },

        // 批量检查区域功能权限
        checkMultipleRegionFunctions() {
            const functions = ['live', 'tvwall', 'breastAI', 'cloudStatistic'];
            const results = {};

            functions.forEach(func => {
                results[func] = this.$checkRegionFunction(func);
            });

            console.log('区域功能权限检查结果:', results);
            return results;
        }
    },

    mounted() {
        // 在组件挂载时检查区域功能权限
        console.log('当前区域:', this.currentRegion);
        console.log('启用的区域功能:', permissionManager.getEnabledRegionFunctions());

        // 检查特定功能
        if (this.$isRegionFunctionEnabled('live')) {
            console.log('直播功能已启用');
        }

        if (this.$isRegionFunctionEnabled('tvwall') && this.$getUserRole() > 1) {
            console.log('电视墙功能已启用且用户有权限');
        }
    }
};
