<template>
<div>
  <el-dialog
      class="cloud_statistics_page"
      :title="$t('cloud_statistics_title')"
      :visible="true"
      :close-on-click-modal="false"
      width="90%"
      :modal="false"
      :before-close="closeCloundStatistics">
        <div class="container">
            <el-tabs v-model="activeName" @tab-click="handleClick" ref="tabs">
                <el-tab-pane :disabled="!isAdmin" v-loading="loadingTab1" :label="$t('cloud_statistics_global_table')" name="1">
                    <div class="data_tabel">
                        <div class="data_row clearfix">
                            <div class="label fl">{{$t('cloud_statistics_statistical_item')}}</div>
                            <div class="value fl">{{$t('cloud_statistics_statistical_item_result')}}</div>
                        </div>
                        <div class="data_row clearfix" v-for="(row,index) of globalData" :key="index">
                            <div class="label fl">{{row.item}}</div>
                            <div class="value fl">{{row.value}}</div>
                        </div>
                    </div>
                </el-tab-pane>
                <el-tab-pane :disabled="!isAdmin" v-loading="loadingTab2" :label="$t('cloud_statistics_interval_table')" name="2">
                    <div class="search_condition clearfix">
                        <div class="select_item fl">
                            <span class="fw">{{$t('hospital_user')}}</span>
                            <el-cascader
                                v-model="hospital_user_selected_section"
                                :options="hospital_user_arr"
                                :disabled="!isAdmin"
                                clearable
                                filterable
								:placeholder="$t('select_placeholder_text')"
                                >
                            </el-cascader>
                        </div>
                        <el-date-picker
                          v-model="search_section_range"
                          type="daterange"
                          align="left"
                          unlink-panels
                          range-separator="-"
                          :start-placeholder="$t('start_time')"
                          :end-placeholder="$t('end_time')"
                          value-format="yyyy-MM-dd"
                          class="picker fl">
                        </el-date-picker>
                    </div>
                    <div class="search_condition clearfix">
                        <el-button type="primary" @click="getSectionData">{{$t('cloud_statistics_inquiry')}}</el-button>
                        <el-button type="success" @click="searchTodaySection">{{$t('cloud_statistics_today')}}</el-button>
                        <el-button type="success" @click="searchWeekSection">{{$t('cloud_statistics_this_week')}}</el-button>
                        <el-button type="success" @click="searchMonthSection">{{$t('cloud_statistics_this_month')}}</el-button>
                    </div>

                    <div class="data_tabel">
                        <div class="data_row clearfix">
                            <div class="label fl">{{$t('cloud_statistics_statistical_item')}}</div>
                            <div class="value fl">{{$t('cloud_statistics_statistical_item_result')}}</div>
                        </div>
                        <div class="data_row clearfix" v-for="(row,index) of sectionData" :key="index">
                            <div class="label fl">{{row.item}}</div>
                            <div class="value fl">{{row.value}}</div>
                        </div>
                    </div>
                </el-tab-pane>
                <el-tab-pane :disabled="!isAdmin" v-loading="loadingTab3" :label="$t('cloud_statistics_diagram')" name="3">
                    <div class="search_condition clearfix">
                        <div class="select_item fl">
                            <span class="fw">{{$t('hospital_user')}}</span>
                            <el-cascader
                                v-model="hospital_user_selected_chart"
                                :options="hospital_user_arr"
                                @change="hospitalUserChartSelectedChange"
                                :disabled="!isAdmin"
                                clearable
                                filterable
                                :placeholder="$t('select_placeholder_text')" >
                            </el-cascader>
                        </div>
                        <div class="select_item fl">
                            <span class="fw">{{$t('cloud_statistics_chart_type')}}</span>
                            <el-select v-model="chart_type" ref="chart_type">
                                <el-option
                                  v-for="item of chart_type_list"
                                  :label="item.label"
                                  :value="item.value"
                                  :key="item.value"
                                  ></el-option>
                            </el-select>
                        </div>
                    </div>
                    <div class="search_condition clearfix">
                        <el-date-picker
                          v-model="search_chart_range"
                          type="daterange"
                          align="left"
                          unlink-panels
                          range-separator="-"
                          :start-placeholder="$t('start_time')"
                          :end-placeholder="$t('end_time')"
                          value-format="yyyy-MM-dd"
                          class="picker fl"
                          @change="searchChartRangeChange" >
                        </el-date-picker>
                        <div class="select_item fl">
                            <span  class="fw">{{$t('cloud_statistics_statistical_item')}}:</span>
                            <el-select multiple :placeholder="$t('select_placeholder_text')" :multiple-limit='3' clearable v-model="statistics_item" ref="statistics_item">
                                <el-option
                                  v-for="item of statistics_item_list"
                                  :key="item.value"
                                  :label="item.label"
                                  :value="item.value"
                                  ></el-option>
                            </el-select>
                        </div>
                        <div class="select_item fl">
                            <span  class="fw">{{$t('cloud_statistics_fineness_with_colon')}}</span>
                            <el-select v-model="statistics_grade" ref="statistics_grade">
                                <el-option
                                  v-for="item of statistics_grade_list"
                                  :label="item.label"
                                  :value="item.value"
                                  :key="item.value"
                                  ></el-option>
                            </el-select>
                        </div>
                    </div>
                    <div class="search_condition clearfix">
                        <el-button type="primary" @click="getChartData">{{$t('cloud_statistics_inquiry')}}</el-button>
                        <el-button type="success" @click="searchTodayChart">{{$t('cloud_statistics_today')}}</el-button>
                        <el-button type="success" @click="searchChartByDays(7)">{{$t('cloud_statistics_last_seven_days')}}</el-button>
                        <el-button type="success" @click="searchChartByDays(30)">{{$t('cloud_statistics_last_thirty_days')}}</el-button>
                        <el-button type="success" @click="searchChartByDays(365)">{{$t('cloud_statistics_last_year')}}</el-button>
                    </div>
                    <div class="char_wrap" ref="char_wrap">
                        <div class="chart_tip"><span>{{chartTip}}</span></div>
                        <div id="clouds_statistics_chart_data" :style="{width:char_wrap.width+'px',height:char_wrap.height+'px'}"></div>
                    </div>
                </el-tab-pane>
                <el-tab-pane v-loading="loadingTab4" :label="$t('group_set_statistics')" name="4">
                    <div class="search_condition clearfix">
                        <div class="select_item fl">
                            <span class="fw">{{$t('groupset_text')}}:</span>
                            <el-select v-model="groupset_list_selected" :placeholder="$t('select_placeholder_text')">
                                <el-option
                                  v-for="item of groupset_list"
                                  :key="item.value"
                                  :label="item.label"
                                  :value="item.value"
                                  ></el-option>
                            </el-select>
                        </div>
                        <div class="select_item fl">
                            <span class="fw">{{$t('cloud_statistics_statistical_item')}}:</span>
                            <el-select v-model="statistics_item_for_ulink" multiple :placeholder="$t('select_placeholder_text')" :multiple-limit='3' clearable ref="statistics_item_for_ulink">
                                <el-option
                                  v-for="item of statistics_item_list_for_ulink"
                                  :key="item.value"
                                  :label="item.label"
                                  :value="item.value"
                                  ></el-option>
                            </el-select>
                        </div>
                    </div>
                    <div class="search_condition clearfix">
                        <div class="select_item fl">
                            <el-date-picker
                              :picker-options="pickerOptions"
                              v-model="search_chart_range_for_ulink"
                              type="daterange"
                              align="left"
                              unlink-panels
                              range-separator="-"
                              :start-placeholder="$t('start_time')"
                              :end-placeholder="$t('end_time')"
                              value-format="yyyy-MM-dd"
                              class="picker"
                              @change="searchChartRangeChangeForUlink" >
                            </el-date-picker>
                        </div>
                        <div class="select_item fl">
                            <span class="fw"> {{$t('cloud_statistics_fineness_with_colon')}}</span>
                            <el-select v-model="statistics_grade_for_ulink" ref="statistics_grade_for_ulink">
                                <el-option
                                  v-for="item of statistics_grade_list_for_ulink"
                                  :label="item.label"
                                  :value="item.value"
                                  :key="item.value"
                                  ></el-option>
                            </el-select>
                        </div>
                        <div class="select_item fl">
                            <span class="fw">{{$t('cloud_statistics_chart_type')}}</span>
                            <el-select v-model="chart_type_for_ulink" ref="chart_type_for_ulink">
                                <el-option
                                  v-for="item of chart_type_list"
                                  :label="item.label"
                                  :value="item.value"
                                  :key="item.value"
                                  ></el-option>
                            </el-select>
                        </div>
                    </div>
                    <div class="search_condition clearfix">
                        <div class="select_item fl">
                            <el-button type="primary" @click="getChartDataForUlink">{{$t('cloud_statistics_inquiry')}}</el-button>
                            <el-button type="success" @click="searchTodayChartForUlink">{{$t('cloud_statistics_today')}}</el-button>
                            <el-button type="success" @click="searchChartByDaysForUlink(7)">{{$t('cloud_statistics_last_seven_days')}}</el-button>
                            <el-button type="success" @click="searchChartByDaysForUlink(30)">{{$t('cloud_statistics_last_thirty_days')}}</el-button>
                            <el-button type="success" @click="searchChartByDaysForUlink(365)">{{$t('cloud_statistics_last_year')}}</el-button>
                            </div>
                    </div>
                    <div class="char_wrap" ref="char_wrap_ulink" >
                        <div class="chart_tip"><span>{{chartTip_for_ulink}}</span></div>
                        <div id="clouds_statistics_chart_data_for_ulink" :style="{width:char_wrap_ulink.width+'px',height:char_wrap_ulink.height+'px'}"></div>
                    </div>
                </el-tab-pane>
                <el-tab-pane v-if="enableIWorks" v-loading="false" :label="$t('cloud_statistics_iworks')" name="5">
                    <div class="full_content">
                        <iframe v-bind:src="iworksStatisticsUrl" class="iframe_content" id="iworks_statistics_iframe"></iframe>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </div>
        <el-dialog
            class="cloud_statistics_detail_dialog"
            width="85%"
            :title="$t('details')"
            :visible.sync="detailDataVisible"
            :before-close="backChartData"
            append-to-body>
            <div class="subContainer" v-loading="loadingDetailData">
                <div class="search_wrapper clearfix">
                    <el-input v-model="detailSearch" @input="filterDetail" :placeholder="$t('search_input_key')" class="fr" clearable></el-input>
                    <span class="fr">{{$t('search')}}：</span>
                </div>
                <div class="data-table-detail">
                    <table>
                        <thead>
                            <tr>
                                <th>{{$t('index_num')}}</th>
                                <th v-for='(item,index) in detailTableHeadData' :key="index"  @click="sort(index)">{{item}}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr v-for='(item,index) in detailFilterData' :key="index">
                                <td>{{index+1}}</td>
                                <td v-for='(it,ind) in detailTableHeadData' :key="ind">{{item[ind]}}</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </el-dialog>
    </el-dialog>
</div>

</template>
<script>
import base from '../lib/base'
import * as echarts from 'echarts/lib/echarts'
import  'echarts/lib/chart/line'
import  'echarts/lib/component/title'
import  'echarts/lib/component/tooltip'
import  'echarts/lib/component/toolbox'
import  'echarts/lib/component/legend'
import  'echarts/lib/component/axisPointer'
import  'echarts/lib/chart/bar'
import {getPickerDate} from '../lib/common_base'
export default {
    mixins: [base],
    name: 'CloudStatistics',
    components: {},
    data(){
        return {
            historylen:history.length,

            prop: null, // last property used by sort
            reverse: false, // reversed sort order
            detailDataVisible: false,
            filerTimer:null,
            loadingDetailData:false,
            detailTableHeadData:{},
            detailSearch:'',
            detailAllData:[],
            detailFilterData:[],
            detailAppendData:{},

            hospital_user_selected_section:[],
            hospital_user_selected_chart:[],
            hospital_user_arr:[],

            activeName:'3',
            loadingTab1:false,
            loadingTab2:false,
            loadingTab3:false,
            loadingTab4:false,
            globalData:[
            ],
            sectionData:[],
            search_section_range:[],
            search_chart_range:[],
            search_chart_range_for_ulink:[],

            char_wrap:{
                width:600,
                height:400
            },
            char_wrap_ulink:{
                width:600,
                height:400
            },
            statistics_item_for_ulink:["19"],
            statistics_item:["10"],
            statistics_grade:"4",
            statistics_grade_for_ulink:"4",
            chart_type:"line",
            chart_type_for_ulink:"line",
            statistics_item_list:[],
            statistics_item_list_for_all:[
                {
                    label:this.$t('cloud_statistics_new_case_count'),
                    value:"10"
                },
                {
                    label:this.$t('cloud_statistics_new_case_count_for_iworks'),
                    value:"36"
                },
                {
                    label:this.$t('cloud_statistics_new_exam_picture'),
                    value:"11"
                },
                {
                    label:this.$t('cloud_statistics_new_exam_video'),
                    value:"12"
                },
                {
                    label:this.$t('cloud_statistics_new_realtime_count'),
                    value:"13"
                },
                {
                    label:this.$t('cloud_statistics_new_group_message_count'),
                    value:"14"
                },
                {
                    label:this.$t('cloud_statistics_online_user_peak'),
                    value:"1"
                },
                {
                    label:this.$t('cloud_statistics_active_group_peak'),
                    value:"3"
                },
                {
                    label:this.$t('cloud_statistics_launch_real_time_consultation'),
                    value:"4"
                },
                {
                    label:this.$t('cloud_statistics_receive_real_time_consultation'),
                    value:"5"
                },

                {
                    label:this.$t('cloud_statistics_client_type_short'),
                    value:"7"
                },
                {
                    label:this.$t('cloud_statistics_new_user_number'),
                    value:"6"
                },
                {
                    label:this.$t('cloud_statistics_new_user_approve_number'),
                    value:"9"
                },
                {
                    label:this.$t('cloud_statistics_active_users_count'),
                    value:"16"
                },
                {
                    label:this.$t('cloud_statistics_ulink_user_total_count'),
                    value:"19"
                },
                {
                    label:this.$t('cloud_statistics_ulink_user_count'),
                    value:"25"
                },
                {
                    label:this.$t('cloud_statistics_ulink_active_user_count'),
                    value:"22"
                },
                {
                    label:this.$t('cloud_statistics_live_user_total_count'),
                    value:"23"
                },
                {
                    label:this.$t('cloud_statistics_active_live_user_count'),
                    value:"24"
                },
                {
                    label:this.$t('cloud_statistics_user_total_count'),
                    value:"20"
                },
                {
                    label:this.$t('cloud_statistics_group_total_count'),
                    value:"21"
                },
                {
                    label:this.$t('cloud_statistics_new_group_number'),
                    value:"2"
                },
                {
                    label:this.$t('cloud_statistics_active_group_count'),
                    value:"15"
                },
                {
                    label:this.$t('cloud_statistics_ulink_install_device_count'),
                    value:"27"
                },
                {
                    label:this.$t('cloud_statistics_ulink_install_device_total_count'),
                    value:"28"
                },
                {
                    label:this.$t('cloud_statistics_live_duration'),
                    value:"29"
                },
                {
                    label:this.$t('cloud_statistics_live_duration_enter'),
                    value:"34"
                },

            ],
            statistics_item_list_for_user:[
                {
                    label:this.$t('cloud_statistics_new_case_count'),
                    value:"10"
                },
                {
                    label:this.$t('cloud_statistics_new_case_count_for_iworks'),
                    value:"36"
                },
                {
                    label:this.$t('cloud_statistics_new_exam_picture'),
                    value:"11"
                },
                {
                    label:this.$t('cloud_statistics_new_exam_video'),
                    value:"12"
                },
                {
                    label:this.$t('cloud_statistics_new_realtime_count'),
                    value:"13"
                },
                {
                    label:this.$t('cloud_statistics_new_group_message_count'),
                    value:"14"
                },
                {
                    label:this.$t('cloud_statistics_launch_real_time_consultation'),
                    value:"4"
                },
                {
                    label:this.$t('cloud_statistics_receive_real_time_consultation'),
                    value:"5"
                },
                {
                    label:this.$t('cloud_statistics_client_type_short'),
                    value:"7"
                },
                {
                    label:this.$t('cloud_statistics_live_duration'),
                    value:"29"
                },
                {
                    label:this.$t('cloud_statistics_live_duration_enter'),
                    value:"34"
                },

            ],
            statistics_item_list_for_ulink:[
                {
                    label:this.$t('cloud_statistics_total_ultra_device_ulink'),
                    value:"33"
                },
                {
                    label:this.$t('cloud_statistics_ulink_user_total_count'),
                    value:"19"
                },
                {
                    label:this.$t('cloud_statistics_live_user_total_count'),
                    value:"23"
                },
                {
                    label:this.$t('cloud_statistics_total_case_count'),
                    value:"30"
                },
                {
                    label:this.$t('cloud_statistics_total_exam_picture'),
                    value:"31"
                },
                {
                    label:this.$t('cloud_statistics_total_exam_video'),
                    value:"32"
                },
                {
                    label:this.$t('cloud_statistics_live_duration'),
                    value:"29"
                },
                {
                    label:this.$t('cloud_statistics_live_duration_enter'),
                    value:"34"
                },
                {
                    label:this.$t('cloud_statistics_ulink_user_count'),
                    value:"25"
                },

                {
                    label:this.$t('cloud_statistics_ulink_active_user_count'),
                    value:"22"
                },
                {
                    label:this.$t('cloud_statistics_active_live_user_count'),
                    value:"24"
                },
            ],
            chart_type_list:[
                {
                    label:this.$t('cloud_statistics_line_chart'),
                    value:"line"
                },
                {
                    label:this.$t('cloud_statistics_bar_chart'),
                    value:"bar"
                },
            ],
            statistics_grade_list:[],
            statistics_grade_list_for_ulink:[],
            statistics_grade_list_for_all:[
                {
                    label:this.$t('cloud_statistics_by_the_week'),
                    value:"4"
                },
                {
                    label:this.$t('cloud_statistics_by_the_month'),
                    value:"2"
                },
                {
                    label:this.$t('cloud_statistics_by_the_day'),
                    value:"1"
                },
                {
                    label:this.$t('cloud_statistics_by_the_hour'),
                    value:"0"
                },
            ],
            statistics_grade_list_without_hour:[
                {
                    label:this.$t('cloud_statistics_by_the_week'),
                    value:"4"
                },
                {
                    label:this.$t('cloud_statistics_by_the_month'),
                    value:"2"
                },
                {
                    label:this.$t('cloud_statistics_by_the_day'),
                    value:"1"
                },
            ],
            statistics_grade_list_with_hour:[
                {
                    label:this.$t('cloud_statistics_by_the_hour'),
                    value:"0"
                },
            ],
            chartOption:{
                title: {
                    text: '',
                    textStyle: {
                        fontSize: 22
                    },
                },
                tooltip: {
                    trigger: 'axis'
                },
                legend: {
                    data:[''],
                    textStyle: {
                        fontSize: 22
                    },
                },
                grid: {
                    left: '6%',
                    right: '7%',
                    // left:'3%',
                    // right:'4%',
                    bottom: '3%',
                    containLabel: true
                },
                toolbox: {
                    "right":'30',
                    feature: {
                        saveAsImage: {}
                    }
                },
                xAxis: {
                    type: 'category',
                    boundaryGap: false,
                    data: [],
                    axisLabel: {
                        fontSize: 22
                    },
                },
                yAxis: {
                    type: 'value',
                    axisLabel: {
                        fontSize: 22
                    },
                },
                series: [
                ],
            },
            myChart:null,
            chartTip:"",
            chartTip_for_ulink:"",
            groupset_id:-1,
            groupset_list_selected:'',
            groupset_list:[],
            groupset_id_to_info_json:{},
            isGroupsetStatistics:false,
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                },
            },
            iworks_statistics_url:"",
        }
    },
    computed:{
        isAdmin(){
            return this.$isAdmin();
        },
        enableIWorks(){
            let b_ret1 = this.groupset_id > 0 ? true : false;
            let b_ret2 = this.systemConfig.serverInfo.enable_statistics ? true : false;
            return b_ret1 && b_ret2;
        },
        iworksStatisticsUrl() {
            if (this.iworks_statistics_url) {
                return this.iworks_statistics_url
            }

            return "about:blank";
        }
    },
    mounted(){
        this.$nextTick(()=>{
            var type = this.$route.params.type;
            var key = 'groupset_id=';
            if(-1 != type.indexOf(key)){//群落统计进入
                this.groupset_id = type.substr(key.length);
                console.log("*************groupset_id: ", this.groupset_id);
                this.activeName = '4';
                this.isGroupsetStatistics = true;
            }else{                      //云端统计进入
                this.activeName = '3';
                this.isGroupsetStatistics = false;
            }

            this.handleClick({
                name:this.activeName
            });

            this.$root.eventBus.$off('iworks_statistics_loaded').$on('iworks_statistics_loaded',this.onIworksStatisticsLoaded);
        })
    },
    methods:{
        closeCloundStatistics(){
            this.onIworksStatisticsLoaded();

            let len = this.historylen - history.length - 1;
            console.log("*********************closeCloundStatistics:go:",len);
            this.$router.go(len);
        },
        sort(prop){
            var data = this.detailFilterData;
            data.sort(function (a, b) {
                if (typeof a[prop] === 'number') {
                    return a[prop] - b[prop];
                }
                a = a[prop].toLowerCase();
                b = b[prop].toLowerCase();
                return a < b ? -1 : a > b ? 1 : 0;
            });
            //  flip decscending order if same sort prop or set to false
            //  if descending reverse the data array
            //  store sort prop
            this.reverse = this.prop === prop ? !this.reverse : false;
            if (this.reverse){
                data.reverse();
            }
            this.prop = prop;
            console.log(prop);
        },
        handleClick(tab){
            if (tab.name=="1") {
                this.getGlobalData()
            }            else if(tab.name=="2"){
                let end=new Date().valueOf();
                end=getPickerDate(end);
                this.search_section_range=['2020-01-01',end];
            }            else if(tab.name=="3"){
                this.$nextTick(()=>{
                    let char_wrap=this.$refs.char_wrap
                    this.char_wrap={
                        width:char_wrap.clientWidth,
                        height:char_wrap.clientHeight,
                    }
                    this.statistics_grade="4"
                    let end=new Date().valueOf();
                    end=getPickerDate(end);
                    this.search_chart_range = ['2020-01-01',end];
                    this.getHospitalUserData()
                    this.searchChartRangeChange();
                })
            }else if(tab.name=="4"){
                this.$nextTick(()=>{
                    let char_wrap_ulink=this.$refs.char_wrap_ulink
                    this.char_wrap_ulink={
                        width:char_wrap_ulink.clientWidth,
                        height:char_wrap_ulink.clientHeight,
                    }
                    this.statistics_grade_for_ulink="4"
                    let end=new Date().valueOf();
                    end=getPickerDate(end);
                    this.search_chart_range_for_ulink = ['2020-01-01',end];
                    this.getGroupSetData()
                    this.searchChartRangeChangeForUlink();
                })
            }else if(tab.name=="5"){
                this.$nextTick(()=>{
                    if("about:blank" == this.iworks_statistics_url || "" == this.iworks_statistics_url) {
                        this.getStatisticsCommand();
                    }
                })
            }
        },
        getGlobalData(){
            this.loadingTab1=true;
            this.globalData=[];
            var that=this
            this.$root.socket.emit("query_clouds_statistics",{},(is_succ,data)=>{
                that.loadingTab1=false;
                if (is_succ) {
                    data.is_total_table = 1;
                    that.globalData=that.getCloudsStatisticsTable(data);
                }
            })
        },
        getSectionData(){
            if (!this.search_section_range) {
                this.$message.error(this.$t('cloud_statistics_range_empty'))
                return
            }
            let params={
                timestamp_begin:this.search_section_range[0],
                timestamp_end:this.search_section_range[1]
            }
            //获取医院用户信息
            if(this.hospital_user_selected_section
                && this.hospital_user_selected_section.length == 2 ){
                var uid = this.hospital_user_selected_section[1] + "";
                if(-1 != uid.indexOf(',')){
                    var arr = uid.substring(0, uid.length-1).split(',');
                    if(arr.length > 0){
                        params.user_id_list = arr;
                    }
                }else{
                    if(uid > 0){
                        params.uid = uid;
                    }
                }
            }

            this.loadingTab2=true;
            this.sectionData=[];
            var that=this
            this.$root.socket.emit("query_clouds_statistics",params,(is_succ,data)=>{
                that.loadingTab2=false;
                if (is_succ) {
                    if(params.uid || params.user_id_list){//根据用户来筛选结果项
                        that.sectionData=that.getCloudsStatisticsTableByUser(data);
                    }else{
                        that.sectionData=that.getCloudsStatisticsTable(data);
                    }
                }
            })
        },
        getCloudsStatisticsTableByUser(data){
            console.log(data);
            let arr = [
                {
                    item:this.$t('cloud_statistics_real_time_consultation_total_time'),
                    value:this.parseTimeStamp(data.event_duration_time.real_time_duration)
                },
                {
                    item:this.$t('cloud_statistics_real_time_consultation_times'),
                    value:data.event_duration_time.real_time_count
                },
                {
                    item:this.$t('cloud_statistics_viewing_total_time'),
                    value:this.parseTimeStamp(data.event_duration_time.enter_real_time_duration)
                },
                {
                    item:this.$t('cloud_statistics_total_time_of_realtinme_voice'),
                    value:this.parseTimeStamp(data.event_duration_time.real_voice_duration)
                },
                {
                    item:this.$t('cloud_statistics_times_of_realtinme_voice'),
                    value:data.event_duration_time.real_voice_count
                },
                {
                    item:this.$t('cloud_statistics_access_realtime_total_time'),
                    value:this.parseTimeStamp(data.event_duration_time.enter_real_voice_duration)
                },
                {
                    item:this.$t('cloud_statistics_chat_messages_number'),
                    value:this.$t('cloud_statistics_chat_messages_text')+ data.text_chat_message_count
                    + '/ '+this.$t('cloud_statistics_chat_messages_frame') + ': ' + data.other_chat_message_count.MEDIC_IMAGE
                    + '/ '+this.$t('cloud_statistics_chat_messages_cine')+ ': '+ data.other_chat_message_count.MEDIC_VIDEO
                    + '/ '+this.$t('cloud_statistics_chat_messages_realtime_review')+ ': '+ data.other_chat_message_count.LIVESTREAM_RECORD_VIDEO
                    + '/ '+this.$t('cloud_statistics_chat_messages_image')+ ': '+ data.other_chat_message_count.IMAGE
                    + '/ '+this.$t('cloud_statistics_chat_messages_realtime_video')+ ': '+ data.other_chat_message_count.VIDEO
                    + '/ '+this.$t('cloud_statistics_chat_messages_sound')+ ': '+ data.other_chat_message_count.VOICE_MESSAGE_FILE
                    + '/ '+this.$t('cloud_statistics_chat_messages_other')+ ': ' + data.other_chat_message_count.other
                },
                {
                    item:this.$t('cloud_statistics_exam_number'),
                    value:data.case_count
                },
                {
                    item:this.$t('cloud_statistics_start_offline_consultation_count'),
                    value:data.start_offline_consultation_count//
                },
                {
                    item:this.$t('cloud_statistics_group_message_count'),
                    value:data.group_message_count
                },
                {
                    item:this.$t('cloud_statistics_comment_count'),
                    value:data.comment_count
                },
                {
                    item:this.$t('cloud_statistics_tags_count'),
                    value:data.tags_count
                },
                {
                    item:this.$t('cloud_statistics_client_type'),
                    value:data.client_type
                },

            ];
            return arr;
        },
        getCloudsStatisticsTable(data){
            let arr = [
                {
                    item:(data.is_total_table==1)?this.$t('cloud_statistics_total_approved_users'):this.$t('cloud_statistics_increased_approved_users'),
                    value:data.users_count
                },
                {
                    item:(data.is_total_table==1)?this.$t('cloud_statistics_total_users'):this.$t('cloud_statistics_increased_users'),
                    value:data.users_count_include_unapproved
                },
                {
                    item:this.$t('cloud_statistics_online_users'),
                    value:data.event_duration_time.online_users_count
                },
                {
                    item:this.$t('cloud_statistics_user_total_online_time'),
                    value:this.parseTimeStamp(data.event_duration_time.user_online_duration)
                },
                {
                    item:(data.is_total_table==1)?this.$t('cloud_statistics_total_conversation'):this.$t('cloud_statistics_increased_conversation'),
                    value:data.conversation_count
                },
                {
                    item:this.$t('cloud_statistics_real_time_consultation_total_time'),
                    value:this.parseTimeStamp(data.event_duration_time.real_time_duration)
                },
                {
                    item:this.$t('cloud_statistics_real_time_consultation_times'),
                    value:data.event_duration_time.real_time_count
                },
                {
                    item:this.$t('cloud_statistics_viewing_total_time'),
                    value:this.parseTimeStamp(data.event_duration_time.enter_real_time_duration)
                },

                {
                    item:this.$t('cloud_statistics_viewing_person_time'),
                    value:data.event_duration_time.enter_real_time_person_time
                },
                {
                    item:this.$t('cloud_statistics_total_time_of_realtinme_voice'),
                    value:this.parseTimeStamp(data.event_duration_time.real_voice_duration)
                },
                {
                    item:this.$t('cloud_statistics_times_of_realtinme_voice'),
                    value:data.event_duration_time.real_voice_count
                },
                {
                    item:this.$t('cloud_statistics_access_realtime_total_time'),
                    value:this.parseTimeStamp(data.event_duration_time.enter_real_voice_duration)
                },
                {
                    item:this.$t('cloud_statistics_access_realtime_person_time'),
                    value:data.event_duration_time.enter_real_voice_person_time
                },
                {
                    item:this.$t('cloud_statistics_chat_messages_number'),
                    value:this.$t('cloud_statistics_chat_messages_text')+ data.text_chat_message_count
                    + this.$t('cloud_statistics_chat_messages_frame')+ data.other_chat_message_count.MEDIC_IMAGE
                    + this.$t('cloud_statistics_chat_messages_cine')+ data.other_chat_message_count.MEDIC_VIDEO
                    + this.$t('cloud_statistics_chat_messages_realtime_review')+ data.other_chat_message_count.LIVESTREAM_RECORD_VIDEO
                    + this.$t('cloud_statistics_chat_messages_image')+ data.other_chat_message_count.IMAGE
                    + this.$t('cloud_statistics_chat_messages_realtime_video')+ data.other_chat_message_count.VIDEO
                    + this.$t('cloud_statistics_chat_messages_sound')+ data.other_chat_message_count.VOICE_MESSAGE_FILE
                    + this.$t('cloud_statistics_chat_messages_other') + data.other_chat_message_count.other
                },
                {
                    item:this.$t('cloud_statistics_exam_number'),
                    value:data.case_count
                },
                {
                    item:this.$t('cloud_statistics_active_group_peak'),
                    value:data.active_group_peak
                },
                {
                    item:this.$t('cloud_statistics_online_user_peak'),
                    value:data.online_user_peak
                },
                {
                    item:this.$t('cloud_statistics_launch_real_time_consultation'),
                    value:data.start_realtime_consultation_peak
                },
                {
                    item:this.$t('cloud_statistics_receive_real_time_consultation'),
                    value:data.enter_realtime_consultation_peak
                },
                {
                    item:(data.is_total_table==1)?this.$t('cloud_statistics_global_group_number'):this.$t('cloud_statistics_new_group_number'),
                    value:data.group_count
                },
                {
                    item:this.$t('cloud_statistics_active_group_count'),
                    value:data.active_group_count
                },

                {
                    item:this.$t('cloud_statistics_start_offline_consultation_count'),
                    value:data.start_offline_consultation_count
                },
                {
                    item:this.$t('cloud_statistics_group_message_count'),
                    value:data.group_message_count
                },
                {
                    item:this.$t('cloud_statistics_comment_count'),
                    value:data.comment_count
                },
                {
                    item:this.$t('cloud_statistics_tags_count'),
                    value:data.tags_count
                },
                {
                    item:this.$t('cloud_statistics_client_type'),
                    value:data.client_type
                },
            ];
            return arr;
        },
        parseTimeStamp(time_of_seconds=0){
            var time = parseInt(time_of_seconds) + this.$t('cloud_statistics_second_text');
            if (parseInt(time_of_seconds) > 60){
                var second = parseInt(time_of_seconds) % 60;
                var minute = parseInt(time_of_seconds / 60);
                time = minute + this.$t('minute_text') + second + this.$t('cloud_statistics_second_text');

                if (minute > 60){
                    minute = parseInt(time_of_seconds / 60) % 60;
                    var hour = parseInt(parseInt(time_of_seconds / 60) / 60);
                    time = hour + this.$t('cloud_statistics_hour_text') + minute + this.$t('minute_text') + second + this.$t('cloud_statistics_second_text');

                    // if (hour > 24){
                    //     hour = parseInt(parseInt(time_of_seconds / 60) / 60) % 24;
                    //     var day = parseInt(parseInt(parseInt(time_of_seconds / 60) / 60) / 24);
                    //     time = day + this.$t('cloud_statistics_day_text') + hour + this.$t('cloud_statistics_hour_text') + minute + this.$t('minute_text') + second + this.$t('cloud_statistics_second_text');
                    // }
                }
            }
            return time;
        },
        searchTodaySection(){
            let today=new Date().valueOf();
            today=getPickerDate(today);
            this.search_section_range=[today,today];
            this.getSectionData()
        },
        searchWeekSection(){
            var myDate_end = new Date();
            var nowDayOfWeek = myDate_end.getDay(); //今天本周的第几天
            var myDate_begin = new Date(myDate_end.getTime() - (nowDayOfWeek-1) * 24 * 3600 * 1000);
            var myDate_week_end = new Date(myDate_end.getTime() + (7-nowDayOfWeek) * 24 * 3600 * 1000);
            var log_begin_date = getPickerDate(myDate_begin);
            var log_end_date = getPickerDate(myDate_week_end);
            this.search_section_range=[log_begin_date,log_end_date];
            this.getSectionData()
        },
        searchMonthSection(){
            var myDate_end = new Date();
            var nowDay = myDate_end.getDate(); //当前日
            var currentMonth = myDate_end.getMonth();//当前月
            var nextMonth = currentMonth +　1;
            var myDate_begin = new Date(myDate_end.getTime() - (nowDay-1) * 24 * 3600 * 1000);
            var nextMonthFirstDay=new Date(myDate_end.getFullYear(),nextMonth,1);
            var myDate_end_m = new Date(nextMonthFirstDay - 24 * 3600 * 1000);
            var log_begin_date = getPickerDate(myDate_begin);
            var log_end_date = getPickerDate(myDate_end_m);
            this.search_section_range=[log_begin_date,log_end_date];
            this.getSectionData()
        },
        getChartDataForUlink(){
            console.log(this.statistics_item_for_ulink);
            if(!this.search_chart_range_for_ulink){
                this.$message.error(this.$t('cloud_statistics_range_empty'))
                return
            }
            if(0 == this.statistics_item_for_ulink.length){
                this.$message.error(this.$t('select_statistics'))
                return
            }

            let params={
                timestamp_begin:this.search_chart_range_for_ulink[0],
                timestamp_end:this.search_chart_range_for_ulink[1],
                statistics_item_list: this.statistics_item_for_ulink,
                statistics_grade:this.statistics_grade_for_ulink,
            }
            if(this.groupset_id_to_info_json[this.groupset_list_selected]
                && this.groupset_id_to_info_json[this.groupset_list_selected].group_id_list
                && this.groupset_id_to_info_json[this.groupset_list_selected].group_id_list.length>0){
                params.group_id_list = this.groupset_id_to_info_json[this.groupset_list_selected].group_id_list;
                params.groupset_id = this.groupset_list_selected;
            }else{
                if(this.groupset_id_to_info_json[this.groupset_list_selected] ){
                    this.$message.error(this.$t('groupset_not_activity'));
                }else{
                    this.$message.error(this.$t('select_groupset'));
                }

                return;
            }

            var that=this;
            this.loadingTab4=true;
            this.$root.socket.emit("query_clouds_statistics_chart_data",params,(is_succ, data)=>{
                console.log("query_clouds_statistics_chart_data ", is_succ);
                console.log(data);
                that.loadingTab4=false;
                if (is_succ) {
                    that.chartOption.series.length = 0;
                    that.chartOption.legend.data.length = 0;
                    if(that.chart_type_for_ulink == "line") {
                        that.chartOption.xAxis.boundaryGap = false;//坐标轴两端的空白策略，无空白
                    }else{
                        that.chartOption.xAxis.boundaryGap = true;
                    }

                    for(var k in data){
                        var item = data[k];
                        if(item.append_data){//含附加数据，用户detail显示
                            that.detailAppendData[k] = item.append_data;
                            item = item.data;
                        }

                        var series = {
                            name:'',
                            type:that.chart_type_for_ulink,
                            stack: '',
                            data:[]
                        }

                        var labels = [];
                        var datas = [];
                        var total = 0;
                        for(var key in item){
                            labels.push(key);
                            series.data.push(item[key]);
                            total += item[key];
                        }

                        var name = that.getStatsName(k);
                        //非累计和非活跃的项目进行总数的统计
                        if(-1 == name.toLowerCase().indexOf(this.$t('accumulation_text')) && -1 == name.toLowerCase().indexOf(this.$t('active')) ){
                            total = Math.round(100*total)/100;//取小数点后两位，四舍五入
                            name += " (" + this.$t('in_total_text') + total + ")";
                        }
                        series.name = name;
                        series.id = k;

                        that.chartOption.xAxis.data=labels;
                        that.chartOption.series.push(series);
                        that.chartOption.legend.data.push(name);
                    }

                    if(that.myChart){
                        that.myChart.clear();
                    }

                    that.myChart = echarts.init(document.getElementById("clouds_statistics_chart_data_for_ulink"))
                    //点击感兴趣的点，获取详细信息
                    that.myChart.off("click");
                    that.myChart.on('click', function (click_params) {
                        console.log("**click");
                        if(that.hasDetailData(click_params.seriesId)){
                            that.detailDataVisible = true;
                            that.getDetailData(click_params, params);
                        }
                    });
                    // 指定图表的配置项和数据
                    that.myChart.setOption(that.chartOption);
                    console.log(JSON.stringify(that.chartOption));

                    //提示消息处理
                    that.chartTip_for_ulink = "";
                    for(var k in data){
                        let label = that.getStatsName(k);
                        if(-1 != label.indexOf(this.$t('cloud_statistics_ulink_active_user_count'))){
                            that.chartTip_for_ulink = this.$t('cloud_statistics_ulink_active_user_count_tip');
                        }else if(-1 != label.indexOf(this.$t('cloud_statistics_active_users_count'))){
                            that.chartTip_for_ulink = this.$t('cloud_statistics_active_users_count_tip');
                        }else if(-1 != label.indexOf(this.$t('cloud_statistics_active_group_count'))){
                            that.chartTip_for_ulink = this.$t('cloud_statistics_active_group_count_tip');
                        }else if(-1 != label.indexOf(this.$t('cloud_statistics_ulink_user'))){
                            that.chartTip_for_ulink = this.$t('cloud_statistics_ulink_user_tip');
                        }
                    }
                }
            })
        },
        getChartData(){
            if (!this.search_chart_range) {
                this.$message.error(this.$t('cloud_statistics_range_empty'))
                return
            }
            if(0 == this.statistics_item.length){
                this.$message.error(this.$t('select_statistics'))
                return
            }
            var that=this;
            this.loadingTab3=true;
            var params={
                timestamp_begin:this.search_chart_range[0],
                timestamp_end:this.search_chart_range[1],
                statistics_item_list: this.statistics_item,
                statistics_grade:this.statistics_grade,
            }
            //获取医院用户信息
            if(this.hospital_user_selected_chart
                && this.hospital_user_selected_chart.length == 2 ){
                var uid = this.hospital_user_selected_chart[1] + "";
                if(-1 != uid.indexOf(',')){
                    var arr = uid.substring(0, uid.length-1).split(',');
                    if(arr.length > 0){
                        params.user_id_list = arr;
                    }
                }else{
                    if(uid > 0){
                        params.uid = uid;
                    }
                }
            }
            this.$root.socket.emit("query_clouds_statistics_chart_data",params,(is_succ, data)=>{
                console.log("query_clouds_statistics_chart_data ", is_succ);
                console.log(data);
                that.loadingTab3=false;
                if (is_succ) {
                    that.chartOption.series.length = 0;
                    that.chartOption.legend.data.length = 0;
                    if(that.chart_type == "line") {
                        that.chartOption.xAxis.boundaryGap = false;//坐标轴两端的空白策略，无空白
                    }else{
                        that.chartOption.xAxis.boundaryGap = true;
                    }
                    for(var k in data){
                        var item = data[k];
                        if(item.append_data){//含附加数据，用户detail显示
                            that.detailAppendData[k] = item.append_data;
                            item = item.data;
                        }

                        var series = {
                            name:'',
                            type:that.chart_type,
                            stack: '',
                            data:[]
                        }

                        var labels = [];
                        var datas = [];
                        var total = 0;
                        for(var key in item){
                            labels.push(key);
                            series.data.push(item[key]);
                            total += item[key];
                        }

                        var name = that.getStatsName(k);

                        //非累计和非活跃的项目进行总数的统计
                        if(-1 == name.toLowerCase().indexOf(this.$t('accumulation_text')) && -1 == name.toLowerCase().indexOf(this.$t('active')) ){
                            total = Math.round(100*total)/100;//取小数点后两位，四舍五入
                            name += " (" + this.$t('in_total_text') + total + ")";
                        }

                        series.name = name;
                        series.id = k;

                        that.chartOption.xAxis.data=labels;
                        that.chartOption.series.push(series);
                        that.chartOption.legend.data.push(name);
                    }

                    if(that.myChart){
                        that.myChart.clear();
                    }
                    that.myChart = echarts.init(document.getElementById("clouds_statistics_chart_data"));
                    //点击感兴趣的点，获取详细信息
                    that.myChart.off("click");
                    that.myChart.on('click', function (click_params) {
                        console.log("**click");
                        if(that.hasDetailData(click_params.seriesId)){
                            that.detailDataVisible = true;
                            that.getDetailData(click_params, params);
                        }
                    });
                    that.myChart.setOption(that.chartOption);
                    console.log(JSON.stringify(that.chartOption));

                    //提示消息处理
                    that.chartTip = "";
                    for(var k in data){
                        let label = that.getStatsName(k);
                        if(-1 != label.indexOf(this.$t('cloud_statistics_ulink_active_user_count'))){
                            that.chartTip = this.$t('cloud_statistics_ulink_active_user_count_tip');
                        }else if(-1 != label.indexOf(this.$t('cloud_statistics_active_users_count'))){
                            that.chartTip = this.$t('cloud_statistics_active_users_count_tip');
                        }else if(-1 != label.indexOf(this.$t('cloud_statistics_active_group_count'))){
                            that.chartTip = this.$t('cloud_statistics_active_group_count_tip');
                        }else if(-1 != label.indexOf(this.$t('cloud_statistics_ulink_user'))){
                            that.chartTip = this.$t('cloud_statistics_ulink_user_tip');
                        }
                    }
                }
            })
        },
        searchTodayChartForUlink(){
            let today=new Date().valueOf();
            today=getPickerDate(today);
            this.search_chart_range_for_ulink=[today,today];
            this.statistics_grade_for_ulink="0"
            this.searchChartRangeChangeForUlink();
            this.getChartDataForUlink()
        },
        searchTodayChart(){
            let today=new Date().valueOf();
            today=getPickerDate(today);
            this.search_chart_range=[today,today];
            this.statistics_grade="0"
            this.searchChartRangeChange();
            this.getChartData()
        },
        searchChartByDaysForUlink(days){
            let today=new Date().valueOf();
            let last=today-(days-1)*24*60*60*1000
            today=getPickerDate(today);
            last=getPickerDate(last);
            if (days==365) {
                this.statistics_grade_for_ulink="2"
            }else{
                this.statistics_grade_for_ulink="1"
            }
            this.search_chart_range_for_ulink=[last,today];
            this.searchChartRangeChangeForUlink();
            this.getChartDataForUlink()
        },
        searchChartByDays(days){
            let today=new Date().valueOf();
            let last=today-(days-1)*24*60*60*1000
            today=getPickerDate(today);
            last=getPickerDate(last);
            if (days==365) {
                this.statistics_grade="2"
            }else{
                this.statistics_grade="1"
            }
            this.search_chart_range=[last,today];
            this.searchChartRangeChange();
            this.getChartData()
        },
        getStatsName(id){
            for(var item of this.statistics_item_list){
                if(item.value == id){
                    return item.label;
                }
            }
            for(var item of this.statistics_item_list_for_ulink){
                if(item.value == id){
                    return item.label;
                }
            }
            return "";
        },
        getGroupSetData(){
            var that = this;
            let params={
                groupset_id: this.groupset_id
            };
            this.groupset_id_to_info_json = {};
            this.groupset_list = [];
            this.$root.socket.emit("query_groupset_info_for_clouds_statistics",params,(is_succ,data)=>{
                if (is_succ) {
                    console.log("**************", data);
                    for(var key in data){
                        that.groupset_list.push({value:data[key].groupset_id, label:data[key].groupset_subject});

                        if(!that.groupset_id_to_info_json.hasOwnProperty(data[key].groupset_id)){
                            that.groupset_id_to_info_json[data[key].groupset_id] = {};
                        }

                        that.groupset_id_to_info_json[data[key].groupset_id].subject = data[key].groupset_subject;
                        that.groupset_id_to_info_json[data[key].groupset_id].group_id_list = data[key].group_id_set;

                    }

                    if(-1 != that.groupset_id){
                        that.groupset_list_selected = parseInt(that.groupset_id);
                    }

                }else{
                    console.log("query_groupset_info_for_clouds_statistics error! ");
                }
            });
        },
        getHospitalUserData(){
            var that = this;
            let params={};
            this.hospital_user_arr = [];
            this.$root.socket.emit("query_hospital_user_info_for_clouds_statistics",params,(is_succ,data)=>{
                that.loadingTab2=false;
                if (is_succ) {
                    var last_hid = -2;
                    var last_json = {};
                    for(var item in data.user_hospital_list){
                        var hid = data.user_hospital_list[item].hid||-1;
                        var hname = data.user_hospital_list[item].hname||this.$t('users_without_hospital');
                        var uid = data.user_hospital_list[item].uid
                        var uname = data.user_hospital_list[item].uname
                        var nickname = data.user_hospital_list[item].nickname
                        var json = {};
                        if(hid == last_hid){
                            last_json.children.push({
                                value:uid,
                                label:uname +'(' + nickname + ')',
                            });
                            if(-1 != hid){
                                last_json.children[0].value += uid + ",";
                            }
                        }else{
                            json.value = hid;
                            json.label = hname;
                            json.children = [];
                            if(-1 != hid){
                                json.children.push({
                                    value:"",
                                    label:this.$t('all_users_of_this_hospital'),
                                });
                            }
                            json.children.push({
                                value:uid,
                                label:uname +'(' + nickname + ')',
                            });
                            if(-1 != hid){
                                json.children[0].value += uid + ",";
                            }

                            last_json = json;
                            last_hid = hid;
                            this.hospital_user_arr.push(json);
                        }
                    }
                    /*var cur_uid = this.user.id;
                    var cur_hid = this.user.hospital_id;
                    if(cur_hid){//当前用户有对应医院
                        this.hospital_user_selected_section = [cur_hid, cur_uid];
                        this.hospital_user_selected_chart = [cur_hid, cur_uid];
                    }else{
                        this.hospital_user_selected_section = [-1, cur_uid];
                        this.hospital_user_selected_chart = [-1, cur_uid];
                    }*/

                    this.hospitalUserChartSelectedChange(this.hospital_user_selected_chart);

                    console.log(this.hospital_user_selected_chart);
                    console.log("hospital_user_arr ", this.hospital_user_arr);
                }
            })
        },
        hospitalUserChartSelectedChange(value){
            console.log("hospital_user_selected_chart ", this.hospital_user_selected_chart);
            console.log("hospitalUserChartSelectedChange ", value);
            if(0 == value.length){//清空选项
                if(this.statistics_item_list != this.statistics_item_list_for_all){
                    this.statistics_item_list = this.statistics_item_list_for_all;
                    this.statistics_item = [];
                }
            }else{
                if(this.statistics_item_list != this.statistics_item_list_for_user){
                    this.statistics_item_list = this.statistics_item_list_for_user;
                    this.statistics_item = [];
                }
            }

        },
        searchChartRangeChange(){
            var startDate = Date.parse(this.search_chart_range[0]);
            var endDate = Date.parse(this.search_chart_range[1]);
            var days=(endDate - startDate)/(1*24*60*60*1000);
            if(days > 0){
                this.statistics_grade_list = this.statistics_grade_list_without_hour;
                if("0" == this.statistics_grade){//原来按小时，现在变为按星期
                    this.statistics_grade="4"
                }
            }else{
                this.statistics_grade_list = this.statistics_grade_list_with_hour;
                this.statistics_grade="0"
            }
            console.log("days: ", days);
        },
        searchChartRangeChangeForUlink(){
            var startDate = Date.parse(this.search_chart_range_for_ulink[0]);
            var endDate = Date.parse(this.search_chart_range_for_ulink[1]);
            var days=(endDate - startDate)/(1*24*60*60*1000);
            if(days > 0){
                this.statistics_grade_list_for_ulink = this.statistics_grade_list_without_hour;
                if("0" == this.statistics_grade_for_ulink){//原来按小时，现在变为按星期
                    this.statistics_grade_for_ulink="4"
                }
            }else{
                this.statistics_grade_list_for_ulink = this.statistics_grade_list_with_hour;
                this.statistics_grade_for_ulink="0";//按小时
            }
            console.log("days: ", days);
        },
        filterDetail(event){
            var that = this;
            clearTimeout(that.filerTimer);
            that.filerTimer=setTimeout(()=>{
                that.detailFilterData=[];
                for(let item of that.detailAllData){
                    for(let key in item){
                        var value = item[key];
                        if(value && value.toString().indexOf(that.detailSearch)!=-1){
                            that.detailFilterData.push(item);
                            break;
                        }
                    }
                }
            },300)
        },
        hasDetailData(statistics_item){
            if(10 == statistics_item   //cloud_statistics_new_case_count
              || 36 == statistics_item   //cloud_statistics_new_case_count_for_iworks
              || 11 == statistics_item //cloud_statistics_new_exam_picture
              || 12 == statistics_item //cloud_statistics_new_exam_video
              || 30 == statistics_item //cloud_statistics_total_case_count
              || 31 == statistics_item //cloud_statistics_total_exam_picture
              || 32 == statistics_item //cloud_statistics_total_exam_video
              || 16 == statistics_item //active_user_count
              || 22 == statistics_item //ulink_active_user_count
              || 24 == statistics_item //active_live_user_count
              || 23 == statistics_item //live_user_total_count
              || 6 == statistics_item //new_users_count_chart
              || 9 == statistics_item //new_approve_users_count_chart
              || 25 == statistics_item //ulink_user_count
              || 19 == statistics_item //ulink_user_total_count
            ){
                return true;
            }else{
                return false;
            }
        },
        getDetailData(click_params, chart_params){
            console.log(click_params);
            console.log(chart_params);

            var that = this;
            that.loadingDetailData=true;

            var detail_params = {
                time: click_params.name,
                statistics_grade: chart_params.statistics_grade,
                statistics_item: parseInt(click_params.seriesId),
                timestamp_begin: chart_params.timestamp_begin,
                timestamp_end: chart_params.timestamp_end,
            }
            if(chart_params.user_id_list){
                detail_params.user_id_list = chart_params.user_id_list;
            }
            if(chart_params.group_id_list){
                detail_params.group_id_list = chart_params.group_id_list;
            }
            if(that.detailAppendData[click_params.seriesId]){
                if(16 == click_params.seriesId){//active_user_count
                    var uid_arr = [];
                    for(var uid in that.detailAppendData[click_params.seriesId][detail_params.time]){
                        uid_arr.push(uid);
                    }
                    detail_params.append_data = uid_arr;
                }else{
                    detail_params.append_data = that.detailAppendData[click_params.seriesId][detail_params.time];
                }
            }

            that.$root.socket.emit("query_clouds_statistics_detail_data",detail_params,(is_succ, data)=>{
                console.log("query_clouds_statistics_detail_data", is_succ);
                console.log(data);
                that.loadingDetailData=false;
                if (is_succ) {
                    if(16 == detail_params.statistics_item){
                        for(var index in data){
                            data[index].online_time = that.detailAppendData[click_params.seriesId][detail_params.time][data[index].user_id];
                        }
                    }
                    that.setDetailTableHead(detail_params.statistics_item);
                    that.detailAllData = data;
                    that.detailFilterData = data;
                }
            });
        },
        setDetailTableHead(statistics_item){
            if(10 == statistics_item   //cloud_statistics_new_case_count
              || 36 == statistics_item   //cloud_statistics_new_case_count_for_iworks
              || 11 == statistics_item //cloud_statistics_new_exam_picture
              || 12 == statistics_item //cloud_statistics_new_exam_video
              || 30 == statistics_item //cloud_statistics_total_case_count
              || 31 == statistics_item //cloud_statistics_total_exam_picture
              || 32 == statistics_item //cloud_statistics_total_exam_video
            ){
                this.detailTableHeadData = {
                    'user_name':this.$t('user_name'),
                    'group_name':this.$t('group_name'),
                    'product_name':this.$t('product_name'),
                    'series_number':this.$t('series_number'),
                    'exam_id':this.$t('exam_id'),
                    'resource_id':this.$t('resource_id'),
                    'time':this.$t('time'),
                }
            }else if(16 == statistics_item){//active_user_count
                this.detailTableHeadData = {
                    'nickname':this.$t('nickname'),
                    'cellphone':this.$t('cellphone'),
                    'hospital_name':this.$t('hospital_name'),
                    'user_id': this.$t('user_id'),
                    'online_time': this.$t('online_time'),
                }
            }else if(22 == statistics_item //ulink_active_user_count
                  || 23 == statistics_item //live_user_total_count
                  || 6 == statistics_item //new_users_count_chart
                  || 9 == statistics_item //new_approve_users_count_chart
                  || 24 == statistics_item //active_live_user_count
                  || 25 == statistics_item //ulink_user_count
                  || 19 == statistics_item //ulink_user_total_count
            ){
                this.detailTableHeadData = {
                    'nickname':this.$t('nickname'),
                    'cellphone':this.$t('cellphone'),
                    'hospital_name':this.$t('hospital_name'),
                    'user_id': this.$t('user_id'),
                }
            }
        },
        backChartData(){
            this.detailFilterData = [];
            this.detailAllData = [];
            this.detailSearch = '';
            this.detailDataVisible = false;
            this.loadingDetailData = false;
        },
        getStatisticsCommand(){
            this.$root.socket && this.$root.socket.emit("get_statistics_command",{groupset_id:this.groupset_id},(err,result)=>{
                console.log("[event] callback in get_statistics_command",err,result);

                if (err) {
                    //失败
                } else {
                    if (result.iworks_statistics_url) {
                        this.iworks_statistics_url = result.iworks_statistics_url;
                        // var iworks_statistics_iframe = document.getElementById("iworks_statistics_iframe");

                        this.iworks_statistics_timer = setTimeout(()=> {
                            this.getStatisticsCommand();
                        }, 10000);
                    } else {
                    }
                }
            });
        },
        onIworksStatisticsLoaded(){
            if (this.iworks_statistics_timer) {
                clearTimeout(this.iworks_statistics_timer);
            }
        },
    }
}
</script>
<style lang="scss">
.cloud_statistics_page{
    &.el-dialog__wrapper{
        .el-dialog{
            margin-top:10vh !important;
            height:80% !important;
            .el-dialog__title{
                font-size:26px;
                line-height:1;
            }
            .container{
                height:100%;
                .data_tabel{
                    border: 1px solid #aaa;
                    overflow:auto;
                    .data_row{
                        border-bottom:1px solid #aaa;
                        .label,.value{
                            width:50%;
                            box-sizing:border-box;
                            padding:8px;
                        }
                        .label{
                            border-right:1px solid #aaa;
                        }
                        &:last-child{
                            border-bottom:none;
                        }
                    }
                }
                .search_condition{
                    margin: 0 10px 10px;
                    font-size: 22px;
                    .el-date-editor{
                        margin-right:20px;
                        margin-top:6px;
                        margin-bottom:6px;
                    }
                    .el-range-input{
                        font-size: 22px;
                    }
                    .el-input{
                        font-size: 22px;
                    }
                    .el-button{
                        font-size: 18px;
                    }
                    .select_item{
                        margin-right:20px;
                        margin-top:6px;
                        margin-bottom:6px;
                    }
                    button{
                        margin-bottom:14px;
                    }
                    .fw{
                        font-weight:bold;
                    }
                }
                .char_wrap{
                    flex:0.95;
                    position:relative;
                    .chart_tip{
                        color:red;
                        font-size: 24px;
                    }
                }
                .back_link{
                    font-size:32px;
                    text-decoration:underline;
                    color:red;
                    cursor:pointer;
                    text-align:center;
                }
                .back_link:hover{
                    color:green;
                }
                .el-tabs{
                    height:100%;
                    .el-tabs__item{
                        font-size:24px
                    }
                    .el-tabs__content{
                        height:calc(100% - 55px);
                        .el-tab-pane{
                            height:100%;
                            display: flex;
                            flex-direction: column;
                            overflow:auto;
                        }
                    }
                }
                .full_content{
                    width:100%;
                    height:100%;
                }
                .iframe_content{
                    width:100%;
                    height:98%;
                }
            }
        }
    }
}
.cloud_statistics_detail_dialog{
    .subContainer{
        height:100%;
        .search_wrapper{
            margin-bottom: 10px;
            .el-input{
                width:300px;
            }
            &>span{
                line-height:40px;
            }
        }
        .data-table-detail{
            height:calc(100% - 55px);
            overflow:auto;
            th,td{
                border:1px solid #bbb;
                font-size: 14px;
                padding: 6px;
                text-align: left;
            }
            table{
                color:#333;
                border:1px solid #bbb;
                width:100%;
                border-collapse: collapse;
                margin-bottom:10px;
                thead{
                    th{
                        background-color:#ccc;
                    }
                }
            }
        }
    }
}
</style>
