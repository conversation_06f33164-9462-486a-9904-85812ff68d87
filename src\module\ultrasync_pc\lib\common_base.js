import { Message } from "element-ui"
import Tool from '@/common/tool.js'
import { cloneDeep } from 'lodash'
import service from '../service/service'
import store from '../store'
import crypto from 'crypto'
import proxyConfig from '../../../../config/proxy'
import moment  from 'moment'
import {newMainDB} from '@/common/indexdb/index.js'
import iworksInternational from '@/common/iworksInternational.js'
import { Buffer } from 'buffer';
import i18n from '@/common/i18n'
import { getLanguage } from '@/common/i18n'
import permissionManager from './permission/index.js'
let root = null
setTimeout(()=>{
    root = window.vm.$root
},0)
export function DEBUG_TO_SERVER(msg, data){
    if (window.main_screen) {
        window.main_screen.gateway.emit("debug", msg, data);
    }
}
export function findServiceId(service_type){
    //找到迈瑞AI的cid
    return new Promise((resolve,reject)=>{
        let chatList=store.state.chatList.list;
        let cid,id;
        for(let item of chatList){
            if (item.service_type==service_type) {
                cid=item.cid;
                break;
            }
        }
        if (!cid) {
            let friendList=store.state.friendList.list;
            for(let item of friendList){
                if (item.service_type==service_type) {
                    id=item.id;
                    break;
                }
            }
        }

        if (cid||id) {
            resolve({
                cid:cid,
                id:id
            })
        }else{
            window.main_screen.controller.emit('query_service_provider_list',(is_succ,data)=>{
                if (is_succ) {
                    if (data.length==0) {
                        Message.error(i18n.t('banned_this_moment'));
                    }
                    for(let item of data){
                        if (item.service_type==service_type) {
                            id=item.id;
                            break;
                        }
                    }
                    resolve({
                        cid:cid,
                        id:id
                    })
                }
            })
        }
    })
}
export function htmlEscape(text = '') {
    var map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };

    // 这次将所有的 & 和 < 都转义，而不排除任何标签
    return text.replace(/[&<>"]/g, function (m) {
        return map[m] || m;
    });
}
export function htmlUnescape(str=''){
    str=str.replace(/&amp;/g,'&')
    str=str.replace(/&lt;/g,'<')
    str=str.replace(/&gt;/g,'>')
    str=str.replace(/&quot;/g,'"')
    str=str.replace(/&#x27;/g,"'")
    str=str.replace(/&#x2F;/g,'/')
    return str
}
export function getLocalImgUrl(img_src){
    return img_src
}
export function getThumb(msg_type){
    let width=window.screen.width;
    const systemConfig = store.state.systemConfig
    let thumb=''
    if (width>1200) {
        thumb='_large'
    }else if (width>=768) {
        thumb='_mid'
    }else{
        return ''
    }
    switch(msg_type){
    case systemConfig.msg_type.Image:
    case systemConfig.msg_type.Video:
        thumb="_thumb"+thumb+".jpg"
        break;
    case systemConfig.msg_type.OBAI:
    case systemConfig.msg_type.Frame:
    case systemConfig.msg_type.Cine:
        thumb="thumbnail"+thumb+".jpg"
        break;
    default:
        break;
    }
    return thumb
}

export  function isSmartIworks(exam) { //画廊内弹提示
    if(exam && exam.exam_type){
        return exam.exam_type ==4 || exam.exam_type ==2;
        // return exam.exam_type ==4 ;
    }
    return false;
}

export function getThumbnailLocalImgUrl(imgObj){
    let img_src=imgObj.url
    let target=getThumb(imgObj.msg_type)
    if (target) {
        img_src=img_src.replace("_thumb.jpg",target)
        img_src=img_src.replace("thumbnail.jpg",target)
    }
    return getLocalImgUrl(img_src);
}
export function getRealUrl(imageObj){
    let realUrl=''
    let msg_type = imageObj.img_type_ex||imageObj.msg_type
    const systemConfig = store.state.systemConfig
    switch(msg_type){
    case systemConfig.msg_type.Image:
        realUrl=imageObj.url.replace(imageObj.thumb,"");
        break;
    case systemConfig.msg_type.OBAI:
        realUrl=imageObj.url.replace("thumbnail.jpg","ScreenShot.jpg");
        break;
    case systemConfig.msg_type.Video:
        realUrl=imageObj.url.replace(imageObj.thumb,imageObj.poster);
        break;
    case systemConfig.msg_type.Frame:
        realUrl=imageObj.url.replace("thumbnail.jpg","SingleFrame.jpg");
        break;
    case systemConfig.msg_type.Cine:
        realUrl=imageObj.url.replace("thumbnail.jpg","DevicePoster.jpg");
        break;
    default:
        realUrl=imageObj.url
        break;
    }
    return realUrl;
}
export function switchRealUrlToBColorUrl(url,index=1){
    const new_url = url.split('/').slice(0, -1).join('/') + '/B_Gray'+index+'.jpg'
    return new_url;
}
export async function getPositionOffsetFromUrl(url){
    const new_url = url.split('/').slice(0, -1).join('/') + '/index.json'
    return new Promise(function(resolve){
        fetch(new_url).then(response => {
            if (response.ok) {
                return response.json(); // 将响应数据转换为JSON
            }
        }).then(json_param => {
            const region = []
            if(json_param&&json_param.ImageWin){
                for (let i = 0; i <json_param.ImageWin.length; i++) {
                    const item = json_param.ImageWin[i]
                    if('X' in item.Region &&  'Y' in item.Region){
                        region.push(item.Region['X'])
                        region.push(item.Region['Y'])
                    }
                }

                if(region.length>0){
                    resolve(region)
                    return
                }
            }
            resolve([0,0])
            return
        }).catch(error => {
            resolve([0,0])
            return
        });
    })
}
export function findProtocolViewNode(protocolTree,protocol_view_guid){
    if (!protocol_view_guid) {
        return null
    }
    if (protocolTree.children.length==0) {
        return null
    }
    let nextChildNodes=[];
    for(let node of protocolTree.children){
        if (node.children) {
            nextChildNodes=nextChildNodes.concat(node.children)
        }
        if (node.GUID==protocol_view_guid) {
            return node;
        }
    }
    return findProtocolViewNode({children:nextChildNodes},protocol_view_guid);
}
export function hasAiAnalyzeResult(item){
    if(item.resource_id){
        let storeItem = store.state.gallery.commentObj[item.resource_id]
        if(storeItem&&storeItem.ai_analyze_report&&storeItem.ai_analyze_report.status){
            return true
        }
    }
    if(item.ai_analyze_report && item.ai_analyze_report.status){
        return true
    }
    return false
}
export function getAiViewsWithExamType(exam){
    let views = null;
    if(isSmartIworks(exam)){
        if(exam.exam_type == 4){
            views = window.vm.$store.state.aiPresetData.iworksAbdomenTest?.views||[]
        }else if(exam.exam_type == 2){
            views = window.vm.$store.state.aiPresetData.cardiacViews?.views||[]
        }
    }
    return views
}

export function setProtocolTree(protocol,countObj){
    if (!protocol) {
        return {};
    }
    let node={}
    if (!countObj) {
        //传递一个计数引用，给每个节点记录顺序
        countObj={
            count:0
        }
    }
    const currentLang = getLanguage() === 'CN'?'CN':'EN';
    const iworksMap = iworksInternational[currentLang];
    node.label=iworksMap[protocol.attributes.name] ||protocol.attributes.name + (protocol.attributes.Suffix||'');
    node.type=protocol.type;
    node.GUID=protocol.attributes.GUID;
    node.LABEL_CEUS=protocol.attributes.LABEL_CEUS;//常规造影
    node.LABEL_CEUS_HIFR=protocol.attributes.LABEL_CEUS_HIFR;//是高帧率造影
    node.StructureID=protocol.attributes.StructureID;//是高帧率造影
    node.id=protocol.id;
    node.disabled=true;
    node.index=countObj.count++
    if (protocol.child_nodes) {
        node.children=[]
        for(let child of protocol.child_nodes){
            node.children.push(setProtocolTree(child,countObj));
        }
    }
    if (2 == protocol.type && protocol.attributes && protocol.attributes.child_nodes) {
        let image_mode = "";
        for(let child of protocol.attributes.child_nodes){
            if (3 == child.type && child.attributes && child.attributes.ImageMode) {
                image_mode = child.attributes.ImageMode;
                break;
            }
        }

        if (image_mode) {
            node.label = image_mode + " " + node.label;
        }
    }
    return node;
}
export function checkIworksTest(exam,protocol=''){
    let flag = false
    if(!exam.exam_type){
        return false
    }
    if(!protocol){
        if(exam.iworks_protocol_execution&&exam.iworks_protocol_execution.child_nodes&&exam.iworks_protocol_execution.child_nodes.length>0){
            protocol = exam.iworks_protocol_execution
            protocol.protocolTree=[setProtocolTree(protocol)]
        }
        if(store.state&&store.state.gallery&&store.state.gallery.iworks_protocol_list&&exam.protocol_execution_guid){
            protocol = protocol||store.state.gallery.iworks_protocol_list[exam.protocol_execution_guid]
        }
        // if(exam.iworks_protocol&&exam.iworks_protocol.child_nodes&&exam.iworks_protocol.child_nodes.length>0){
        //     protocol = exam.iworks_protocol
        // }
    }
    let views = getAiViewsWithExamType(exam);
    if(protocol&&views){
        let child_nodes = protocol.child_nodes
        child_nodes.map(item=>{
            if(flag){
                return true;
            }
            if(item.attributes&&item.attributes.StructureID){
                views.map(v=>{
                    if(v.key == item.attributes.StructureID){
                        flag = true
                        return true
                    }
                })
            }
        })
    }
    return flag
}
export function findProtocolViewNodeByStructId(protocolTree,structure_id){
    if (!structure_id) {
        return null
    }
    if (protocolTree.children.length==0) {
        return null
    }
    let nextChildNodes=[];
    for(let node of protocolTree.children){
        if (node.children) {
            nextChildNodes=nextChildNodes.concat(node.children)
        }
        // console.error('attributes')
        if (node&&node.StructureID && node.StructureID==structure_id) {
            return node;
        }
    }
    return findProtocolViewNode({children:nextChildNodes},structure_id);
}
export function setIworksInfoToMsg(msg,protocol_execution_guid){
    let guid=protocol_execution_guid || msg.protocol_execution_guid
    let protocolInfo=''
    if(store.state&&store.state.gallery&&store.state.gallery.iworks_protocol_list){
        protocolInfo = store.state.gallery.iworks_protocol_list[guid]
    }
    const currentLang = getLanguage() === 'CN'?'CN':'EN';
    const iworksMap = iworksInternational[currentLang];
    msg.protocol_name=iworksMap[msg.protocol_name] || msg.protocol_name;
    if (guid && !protocolInfo) {
        //协议信息还没推送到
        setTimeout(()=>{
            setIworksInfoToMsg(msg);
        },300)
        return ;
    }else if (protocolInfo) {
        let protocolTree=protocolInfo.protocolTree[0]
        msg.protocol_node_id=0
        if (protocolTree) {
            let node=findProtocolViewNode(protocolTree,msg.protocol_view_guid)
            if (node) {
                msg.protocol_view_name=node.StructureID&&iworksMap[node.StructureID]? iworksMap[node.StructureID] : node.label
                msg.protocol_node_id=node.id
            }else{
                msg.protocol_view_name= '';
                let iworksTestFlag = checkIworksTest({iworks_protocol_execution:protocolInfo,exam_type:msg.exam_type})
                if(iworksTestFlag&& isSmartIworks(msg) && hasAiAnalyzeResult(msg)){
                    //腹部检查-存在ai数据
                    let storeItem = store.state.gallery.commentObj[msg.resource_id]||msg
                    let ai_analyze_report = storeItem.ai_analyze_report
                    let structure_id = ''
                    if(ai_analyze_report
                        &&ai_analyze_report.clips
                        &&ai_analyze_report.clips[msg.resource_id]
                        &&ai_analyze_report.clips[msg.resource_id][0]
                        &&ai_analyze_report.clips[msg.resource_id][0].clip_id){
                        structure_id = ai_analyze_report.clips[msg.resource_id][0].clip_id
                    }

                    msg.isIworksTest = iworksTestFlag
                    if (protocolInfo&&structure_id) {
                        let protocolTree=protocolInfo.protocolTree[0]
                        let node=findProtocolViewNodeByStructId(protocolTree,structure_id)
                        if (node) {
                            msg.protocol_view_name=node.StructureID&&iworksMap[node.StructureID]? iworksMap[node.StructureID] : node.label
                            msg.protocol_node_id=node.id
                            msg.LABEL_CEUS=node.LABEL_CEUS
                            msg.LABEL_CEUS_HIFR=node.LABEL_CEUS_HIFR
                            msg.protocol_view_guid=node.GUID
                            msg.protocol_execution_guid=protocolInfo.attributes.GUID
                            msg.protocol_node_id=node.id
                        }
                    }
                }
            }
        }
    }
}
export function setExpirationResource(data,cid){ // 设置失效资源类型
    const systemConfig = store.state.systemConfig
    data=data.map(item=>{
        if (item.msg_type==systemConfig.msg_type.EXAM_IMAGES) {
            return item;
        }
        if(item.resource_deleted ==true){
            item.msg_type = systemConfig.msg_type.EXPIRATION_RES // 失效资源类型
        }
        if(item.ai_analyze&&item.ai_analyze.messages){
            item.ai_analyze.messages = item.ai_analyze.messages.reduce((h,v)=>{
                if(v.resource_deleted ==true){
                    v.msg_type = systemConfig.msg_type.EXPIRATION_RES // 失效资源类型
                }
                h.push(v)
                return h
            },[])
        }
        return item
    })
    return data
}
export function addRootToUrl(img_src){
    if (img_src&&!/http/.test(img_src)&&img_src[0]!='/') {
        img_src='/'+img_src
    }
    return img_src;
}
export function parseImageListToLocal(list,key){
    if (list != undefined){
        for(let item of list){
            if(item[key]){
                //有local就不替换
                const encode_type = item.img_encode_type
                item[key]=addRootToUrl(item[key]);
                if (!item[key+'_local'] || item[key+'_local'] == "undefined") {
                    item[key+'_local']=getLocalImgUrl(item[key])
                }
                if(encode_type && encode_type.toUpperCase() === 'DCM'){
                    // dcm文件展示默认图,error事件更改默认图的方案有问题，在这里补足
                    item.error_image= 'static/resource_pc/images/file_icon/dcm.png';
                    window.vm.$store.commit("resourceTempStatus/updateResourceTempStatus",{
                        resource_id:item.resource_id,
                        data:{
                            error_image:'static/resource_pc/images/file_icon/dcm.png',
                            loaded:true
                        }
                    });
                }
            }
        }
    }
    return list;
}
export function formatString(text, param){
    return text.replace(/\{(\d+)\}/gm, function(ms, p1){
        return typeof(param[p1]) == 'undefined' ? ms : param[p1]
    });
}
export function closeChatWindowIfNeed(cid){
    if (window.vm.$route.fullPath.indexOf(`chat_window/${cid}`)>=0) {
        window.vm.$router.replace('/main/index/chat_window/0')
    }
}
export function initImportExamImageTempQueue(list,callback){
    console.log("[event] initImportExamImageTempQueue", list);
    const systemConfig = store.state.systemConfig
    const user = store.state.user
    if (0 == list.length) {
        return;
    }
    root.importExamImageTempQueue = [];
    let queueTemp = root.importExamImageTempQueue;

    let uid = user.uid;
    let total_count = 0;
    let ignore_count = 0;

    for (let i in list) {
        let item = list[i];
        total_count++;

        if (item.sender_id != uid
            || item.msg_type != systemConfig.msg_type.RealTimeVideoReview) {
            ignore_count++;
            continue;
        }

        queueTemp.push(item);
    }
    if (0 < ignore_count) {
        if (ignore_count == total_count) {
            let tip = i18n.t('exam_manager.error.single_selected_unmatch');
            window.vm.$confirm(
                tip,
                i18n.t('tip_title'),
                {
                    confirmButtonText:i18n.t('confirm_button_text'),
                    cancelButtonText:i18n.t('cancel_button_text'),
                    type:'warning'
                }
            ).then(()=>{
                callback && callback(null);
            }).catch(()=>{
            })
        } else {
            let tip = formatString(i18n.t('exam_manager.error.mutiple_selected_unmatch'), {1:ignore_count});
            window.vm.$confirm(
                tip,
                i18n.t('tip_title'),
                {
                    confirmButtonText:i18n.t('confirm_button_text'),
                    cancelButtonText:i18n.t('cancel_button_text'),
                    type:'warning'
                }
            ).then(()=>{
                callback && callback(null);
            }).catch(()=>{
            })
        }
    } else {
        callback && callback(null);
    }
}
export function transferPatientInfo(param){
    if (!param.patient_id) {
        return {};
    }
    let obj={}
    obj.patient_name=param.patient_name||i18n.t('not_upload_text');
    obj.patient_name_str=param.patient_name||i18n.t('exam_patient_name')+': '+i18n.t('not_upload_text');
    switch(param.patient_sex){
    case 0:
        obj.patient_sex = i18n.t('male');
        obj.patient_sex_str = i18n.t('male');
        break;
    case 1:
        obj.patient_sex = i18n.t('female');
        obj.patient_sex_str = i18n.t('female');
        break;
    default :
        obj.patient_sex = i18n.t('not_upload_text');
        obj.patient_sex_str = i18n.t('exam_patient_sex')+':' +i18n.t('not_upload_text');
        break;
    }
    if (param.hasOwnProperty('patient_age')&& param.patient_age!=-1 ) {
        var  patient_age_unit=''
        switch (param.patient_age_unit) {
        case 21003:
            patient_age_unit = i18n.t('age_unit_year');
            break;
        case 21002:
            patient_age_unit = i18n.t('age_unit_month');
            break;
        case 21000:
            patient_age_unit = i18n.t('cloud_statistics_day_text');
            break;
        default :
            break;
        }
        obj.patient_age = param.patient_age + patient_age_unit;
        obj.patient_age_str=obj.patient_age = param.patient_age + patient_age_unit;
    } else {
        obj.patient_age = i18n.t('not_upload_text');
        obj.patient_age_str =i18n.t('exam_patient_age')+ i18n.t('not_upload_text');
    }
    if(param.hasOwnProperty('exam_custom_info')){
        obj.exam_custom_info = param.exam_custom_info
    }
    return obj;
}
export function cancelFavoriteCommit(target,callback){
    service.cancelFavorite({
        resourceIDList:[target.resource_id]
    }).then((res)=>{
        if (res.data.error_code==0) {
            window.vm.$store.commit('resourceTempStatus/updateResourceTempStatus',{
                resource_id:target.resource_id,
                data:{
                    userFavoriteStatus:false,
                }
            })
            callback&&callback();

        }
    })
}
export function getPickerDate(timestamp){
    let time=new Date(timestamp);
    let year=time.getFullYear();
    let month=time.getMonth()+1;
    let day=time.getDate();
    month=month>9?month:'0'+month;
    day=day>9?day:'0'+day;
    return year+'-'+month+'-'+day;
}
export function getTimestamp(){
    var now=new Date()
    let year=now.getFullYear()
    let month=now.getMonth()+1
    let day=now.getDate();
    let hour=now.getHours();
    let minutes=now.getMinutes();
    let second=now.getSeconds();
    return {
        time:`${year}-${month>9?month:"0"+month}-${day>9?day:"0"+day} ${hour>9?hour:"0"+hour}:${minutes>9?minutes:"0"+minutes}:${second>9?second:"0"+second}`,
        timestamp:now.valueOf()
    }
}
export function sortFriendList(){
    let list=store.state.friendList.list
    let online=[]
    let offline=[]
    for(let item of list){
        if (item.state==1||item.service_type) {
            online.push(item)
        }else{
            offline.push(item)
        }
    }
    list=online.concat(offline);
    store.commit('friendList/initFriendList',list);
}
export function openVisitingCard(data,type){
    //type:1 聊天记录点击消息头像进入
    //type:2 好友列表点击头像进入
    //type:3 群成员列表点击头像进入
    //type:4 全局搜索好友点击头像进入
    //type:5 点击加入群聊系统提示被邀请人名称进入
    //type:6 点击加入群聊系统提示邀请人名称进入
    const cid = data.group_id||data.groupid;
    if(Number(window.vm.$root.currentLiveCid) === Number(cid)){
        this.$message.error(i18n.t('playing_video_tip'))
        return
    }
    let user={};
    let target=''
    let path=window.vm.$route.path

    const conversation = store.state.conversationList[cid]
    if (window.vm.$route.params.groupset_id) {
        path='/main/index/chat_window/0'
    }
    let userid=0;
    switch(type){
    case 1:
        userid=data.sender_id
        user=Object.assign({},conversation.attendeeList['attendee_'+userid]);
        break;
    case 2:
        user=Object.assign({},data);
        user.userid=data.id;
        break;
    case 4:
        user=Object.assign({},data);
        break;
    case 3:
        userid=data.userid
        user=Object.assign({},conversation.attendeeList['attendee_'+userid]);
        break;
    case 5:
        userid=data.attendee_changed_info.user_id
        user=Object.assign({},conversation.attendeeList['attendee_'+userid])
        break;
    case 6:
        userid=data.attendee_changed_info.inviter_id || data.attendee_changed_info.qr_code_owner_id || data.attendee_changed_info.sharer_id
        user=Object.assign({},conversation.attendeeList['attendee_'+userid])
        break;
    case 7:
        user=data;
        for(let friend of window.vm.$store.state.friendList.list){
            if (user.id===friend.id) {
                user=friend;
                break;
            }
        }
        break;
    default :
        break;
    }

    target=path+'/visiting_card'
    store.commit('relationship/updatePersonalObj',user);
    window.vm.$router.push({
        path:target,
        query:window.vm.$route.query
    });
}
export function pushImageToList(message,ignoreConsultationImages){
    //图片或视频信息放入总图像列表和会话图像列表
    console.log('pushImageToList',message)
    const systemConfig = store.state.systemConfig
    try {
        const cid = message.group_id
        const conversation = store.state.conversationList[cid]

        if(!conversation){
            return
        }
        if(message.msg_type === systemConfig.msg_type.WITHDRAW){
            return
        }
        if (message.msg_type==systemConfig.msg_type.EXAM_IMAGES) {
            message=cloneDeep(message);
            message.msg_type=message.cover_msg_type;
        }
        if (message.msg_type==systemConfig.msg_type.Image||
            message.msg_type==systemConfig.msg_type.Frame||
            message.msg_type==systemConfig.msg_type.OBAI||
            message.msg_type==systemConfig.msg_type.Cine||
            message.msg_type==systemConfig.msg_type.Video||
            message.msg_type==systemConfig.msg_type.RealTimeVideoReview||
            message.msg_type==systemConfig.msg_type.VIDEO_CLIP
        ) {
            let conversationImages=conversation.galleryObj.gallery_list
            let consultationImages=store.state.consultationImageList.list;
            let isRepeatConversation=false
            let isRepeatConsultation=false
            if (ignoreConsultationImages){
                //AI分析图片不放入总图像列表里
                isRepeatConsultation=true;
            }
            for(let img of conversationImages){
                if (img.resource_id==message.resource_id) {
                    isRepeatConversation=true;
                    break;
                }
            }
            for(let img of consultationImages){
                if (img.resource_id==message.resource_id) {
                    isRepeatConsultation=true;
                    break;
                }
            }
            if (!isRepeatConversation) {
                let commentObj={}
                commentObj={
                    ai_analyzing:ignoreConsultationImages||false,//正在AI分析的标记，分析返回后去除
                    ai_analyze_report:message.ai_analyze_report||{},
                    tag_names:message.tag_names,
                    comment_list:message.comment_list||[],
                    tags_list:message.tags_list||[]
                }
                if(message.ai_analyze_report&&message.ai_analyze_report.list){
                    commentObj.showAISearchSuggest = message.ai_analyze_report.list.length>0
                }
                store.commit('gallery/updateCommentToGallery',{
                    obj:commentObj,
                    resource_id:message.resource_id
                });
            }
            setTimeout(function(){
                //延时放入图像列表，否则当会话界面打开且收到图片消息时，同时更新src会出现问题
                if(!isRepeatConsultation){
                    //图片在图像列表中未存在
                    store.commit('consultationImageList/addFileToConsultationImages',message);
                    root.eventBus.$emit('listenerGalleryNewData',{data:message,tag:'consultation'})
                }
                if (!isRepeatConversation) {
                    //在会话的图像列表中未存在
                    store.commit('conversationList/addFileToConversation',{
                        message:message,
                        cid:message.group_id
                    });
                    root.eventBus.$emit('listenerGalleryNewData',{data:message,tag:'conversation'})
                }
                if (!message.resource_quote) {
                    root.eventBus.$emit('updateGroupsetWallIfNeed',message)
                }

            },1000)
        }
    } catch (error) {
        console.error(error)
    }

}
export function getFileIcon(file_name){
    let file_type=file_name.replace(/.+\./,"").toLowerCase()
    return `static/resource_pc/images/file_icon/${file_type}.png`
}
export function noFileIcon(event){
    event.currentTarget.src='static/resource_pc/images/file_icon/temp.png'
}
export function getDefaultPreferences(user){
    console.log('user',user)
    let default_pre={
        auto_upload:'0',
        default_conversation:{}
    }
    let preferences = user.preferences;
    if (typeof preferences =='string') {
        preferences=JSON.parse(preferences);
        if (preferences.default_conversation) {
            preferences.default_conversation=JSON.parse(preferences.default_conversation)
        }
    }
    if (preferences) {
        Object.keys(preferences).forEach((key)=>{
            default_pre[key]=preferences[key];
        })
    }
    return default_pre;
}

export function handleAfterLogin(user,isRemember){
    window.vm.$store.commit('dynamicGlobalParams/updateToken',user.new_token)
    if (isRemember) {
        window.localStorage.setItem('loginToken',user.new_token)
        window.localStorage.setItem('account',user.username)
        window.localStorage.setItem('password','')
    }else{
        window.localStorage.setItem('loginToken','')
        window.localStorage.setItem('account','')
        window.localStorage.setItem('password','')
    }
    user.preferences=getDefaultPreferences(user);
    store.commit('user/updateUser', user);

    // 初始化权限管理器
    if (user && user.uid) {
        console.log('用户登录成功，初始化权限管理器:', user.uid, user.role);
        permissionManager.initialize(user).then(() => {
            console.log('权限管理器初始化成功');
        }).catch(error => {
            console.error('权限管理器初始化失败:', error);
        });
    }

    newMainDB(user.uid)
    if (!user.is_enhance_password) {
        setTimeout(()=>{
            let tip=i18n.t('enhance_password_tip')
            window.vm.$MessageBox.alert(tip).then(()=>{
                // this.$router.replace(`/index/chat_window/0`);
                window.vm.$router.push(`/main/index/chat_window/0/personal_setting?force_enhance_password`);
            }).catch(()=>{
                window.vm.$router.push(`/main/index/chat_window/0/personal_setting?force_enhance_password`);
            })
        },1000)
    }
    const probationary_expiry = user.probationary_expiry;
    const referralCode = store.state.globalParams.functionsStatus.referralCode;
    if (referralCode && probationary_expiry && 0 < probationary_expiry.length) {
        const tip =  formatString(i18n.t('nls_probationary_expiry_tip'), {
            1:probationary_expiry
        });
        setTimeout(()=>{
            window.vm.$MessageBox.alert(tip)
        })
    }

}
export function patientDesensitization(list){
    const user = store.state.user
    const isDesensitization=user.preferences&&user.preferences.is_desensitization||0;
    if (isDesensitization==0) {
        //不脱敏直接返回
        return list;
    }
    list.forEach(image=>{
        if (image.patient_name) {
            image.patient_name='***'
        }
    })
    return list;
}

export function setDialogWrapperPosition(isSet){ //画廊下 设置弹窗位置
    let containerDom1 =  document.querySelector('.el-message-box__wrapper')
    let childom1 = containerDom1.querySelector('.el-message-box')
    const systemConfig = store.state.systemConfig
    const setWrapperDomPosition = function(containerDom,childom,type){
        if(type === 1){
            containerDom.style.width = '90%'
            containerDom.style.marginTop = '0px'
            containerDom.style.marginBottom = '0px'
            containerDom.style.marginLeft = 'auto'
            containerDom.style.marginRight = 'auto'
            childom.style.position = 'absolute'
            childom.style.top="40%"
            childom.style.right = "1.4rem"
        }else{
            containerDom.style.width = 'auto'
            containerDom.style.margin = '0'
            childom.style.position = 'static'
            childom.style.top="auto"
            childom.style.right = "auto"
        }

    }
    if(isSet){
        let isGallery = systemConfig.gallery_image_mode.RealTimeVideo == store.state.realtimeVideo.gallery_image_mode
        if(!isGallery){
            return
        }
        setWrapperDomPosition(containerDom1,childom1,1)
    }else{
        setTimeout(() => {
            setWrapperDomPosition(containerDom1,childom1,0)
        }, 500);
    }
}
export function checkVideoDevice({isMustHaveCamera=true}) { // isMustHaveCamera 是否必须要有摄像头才允许进行
    return new Promise((resolve, reject) => {
        window.CWorkstationCommunicationMng.GetCameraDevice()
        // const current_scan_room_controller = window.main_screen.CScanRoom.currentScanRoom();
        // const ultrasync_box_ip_addr = current_scan_room_controller&&current_scan_room_controller.ultrasync_box_ip_addr||undefined
        // const ultrasync_box_listening_port = current_scan_room_controller&&current_scan_room_controller.ultrasync_box_cmd_port||undefined
        setTimeout(() => {
            const cameraDeviceListFromApp = store.state.device.camera.filter(item=>!item.exclude)
            const currentSelectCamera = localStorage.getItem('defaultCameraDevice')
            if (cameraDeviceListFromApp.length == 0) {
                if(!isMustHaveCamera){
                    resolve(cameraDeviceListFromApp)
                }else{
                    window.vm.$message.error(i18n.t('no_videocamera_be_detected'))
                    reject(cameraDeviceListFromApp)
                }

            }else if (!currentSelectCamera) { // 没有设置默认摄像头，引导选择
                if(cameraDeviceListFromApp.length == 1){ //只有一个摄像头则默认选中
                    window.CWorkstationCommunicationMng.SetCameraDevice({
                        name: cameraDeviceListFromApp[0].name,
                        path: cameraDeviceListFromApp[0].path,
                        // ultrasync_box_ip_addr,
                        // ultrasync_box_listening_port
                    })
                    resolve(cameraDeviceListFromApp)
                }else{
                    // this.$message.error(i18n.t('videocamera_muti_devices'))
                    let cid=window.vm.$route.query.cid||window.vm.$route.params.cid
                    window.vm.$router.push(`/main/index/chat_window/${cid}/equipment_detection/0?tab=third`)
                    reject(cameraDeviceListFromApp)
                }


            }else if(currentSelectCamera){
                let isMatch = false
                cameraDeviceListFromApp.forEach(item=>{
                    if(item.path===currentSelectCamera){
                        window.CWorkstationCommunicationMng.SetCameraDevice({
                            name: item.name,
                            path: item.path,
                            // ultrasync_box_ip_addr,
                            // ultrasync_box_listening_port
                        })
                        isMatch = true
                        resolve(cameraDeviceListFromApp)
                        return
                    }
                })
                if(!isMatch){
                    let cid=window.vm.$route.query.cid||window.vm.$route.params.cid
                    window.vm.$router.push(`/main/index/chat_window/${cid}/equipment_detection/0?tab=third`)
                    reject(cameraDeviceListFromApp)
                }
            }else{
                let cid=window.vm.$route.query.cid||window.vm.$route.params.cid
                window.vm.$router.push(`/main/index/chat_window/${cid}/equipment_detection/0?tab=third`)
                reject(cameraDeviceListFromApp)
            }
        }, 500)
    })
}
export function getAudioDeviceExceptionInfo(error_info){
    const systemConfig = store.state.systemConfig
    var AudioDeviceCheck = systemConfig.AudioDeviceCheck;
    var tip = "音频设备检测异常";
    if(AudioDeviceCheck.enum_exception == error_info){
        tip = "音频设备检测异常";
    }else if(AudioDeviceCheck.no_input_output_device == error_info){
        tip = "请插入音频设备"
    }else if(AudioDeviceCheck.no_output_device == error_info){
        tip = "请插入扬声器"
    }else if(AudioDeviceCheck.no_input_device == error_info){
        tip = "请插入麦克风"
    }

    return tip;
}
export function checkAudioDevice(fn){
    console.log("checkAudioDevice");
    const systemConfig = store.state.systemConfig
    const user = store.state.user
    //枚举获取输入输出设备
    var media_devices = {audioinput: [], audiooutput: []};
    var default_input_device_name = "";
    var default_output_device_name = "";
    var AudioDeviceCheck = systemConfig.AudioDeviceCheck;

    navigator.mediaDevices.enumerateDevices()
        .then(function(devices) {
            devices.forEach(function(device) {
                if(("default" != device.deviceId) && ("communications" != device.deviceId) ){
                    if ('audiooutput' === device.kind) {
                        media_devices.audiooutput.push(device);
                    } else if ('audioinput' === device.kind) {
                        media_devices.audioinput.push(device);
                    }
                }else if(("default" == device.deviceId)){
                    if ('audioinput' === device.kind) {
                        default_input_device_name = Tool.getNameForAudioDeviceLabel(device.label);
                    } else if ('audiooutput' === device.kind) {
                        default_output_device_name = Tool.getNameForAudioDeviceLabel(device.label);
                    }
                }
            });

            var input_name = "";
            var output_name = "";
            if(0 == media_devices.audioinput.length || 0 == media_devices.audiooutput.length){ //无输入或输出设备，不处理
                var error_info = AudioDeviceCheck.no_input_output_device ;
                if(0 == media_devices.audioinput.length && 0 == media_devices.audiooutput.length){
                    error_info = AudioDeviceCheck.no_input_output_device;
                }else if(0 == media_devices.audiooutput.length){
                    error_info = AudioDeviceCheck.no_output_device;
                }else{
                    error_info = AudioDeviceCheck.no_input_device;
                }

                DEBUG_TO_SERVER("[RT-Voice-Client] Exception: uid(" + user.uid + ") " + getAudioDeviceExceptionInfo(error_info));
                fn({error:1, error_info:error_info});
                return;
            } else if((1 == media_devices.audioinput.length) && (1 == media_devices.audiooutput.length)){//一个输入和一个输出设备，不处理，程序继续运行，
                // do nothing
                //麦克风 (2- Logitech Wireless Headset) (046d:0a29)
                input_name = Tool.getNameForAudioDeviceLabel( media_devices.audioinput[0].label);
                output_name = Tool.getNameForAudioDeviceLabel( media_devices.audiooutput[0].label);
            }else if(1 < media_devices.audiooutput.length || 1 < media_devices.audioinput.length){ //有多个输入或输出设备，首先判断是否做过声音设备检测，没有检测过则弹出对话框进行检查
                //首先判断是否做过声音设备检测，没有检测过则弹出对话框进行检查
                var is_do_audio_detect = localStorage.getItem('detect_audio_device');
                if(1 != is_do_audio_detect){
                    fn({error:1, error_info:AudioDeviceCheck.has_mult_input_output_device});
                    return;
                }
                //有声音设备检测记录
                var device_info = localStorage.getItem('last_audio_device_info');
                if(device_info && "" != device_info){
                    var json_device_info = JSON.parse(device_info);
                    var already_set_input_device_name = json_device_info.input;
                    var already_set_output_device_name = json_device_info.output;

                    if(already_set_input_device_name == "" || already_set_output_device_name == ""){
                        fn({error:1, error_info:AudioDeviceCheck.has_mult_input_output_device});
                        return;
                    }

                    var find_input_device = false;
                    var find_output_device = false;
                    for(var i=0; i < media_devices.audioinput.length; i++){
                        if(-1 != media_devices.audioinput[i].label.indexOf(already_set_input_device_name)){
                            find_input_device = true;
                            break;
                        }
                    }
                    for(var j=0; j < media_devices.audiooutput.length; j++){
                        if(-1 != media_devices.audiooutput[j].label.indexOf(already_set_output_device_name)){
                            find_output_device = true;
                            break;
                        }
                    }

                    if(!find_input_device || !find_output_device){ //记录中的设备不在线时，删除记录，执行没有记录的规则 //else 记录中的设备依然在线时，使用记录的麦克风
                        localStorage.setItem('last_audio_device_info', "");
                        fn({error:1, error_info:AudioDeviceCheck.has_mult_input_output_device});
                        return;
                    }

                    input_name = already_set_input_device_name;
                    output_name = already_set_output_device_name;
                }else{//若未设置过音频设备
                    fn({error:1, error_info:AudioDeviceCheck.has_mult_input_output_device});
                    return;
                }
            }

            fn({error:0, input_name:input_name, output_name:output_name});
        })
        .catch(function(err) {
            console.log("无法枚举输入输出设备");
            console.log(err.name + ": " + err.message);
            DEBUG_TO_SERVER("[RT-Voice-Client] error: 无法枚举输入输出设备 err.name=" + err.name+ " err.message=" + err.message);
            fn({error:1, error_info:AudioDeviceCheck.enum_exception}) ;
        });
}
export function popDeviceCheckWindow(){
    Tool.loadModuleRouter(window.vm.$route.fullPath+'/equipment_detection/0')
}
export function getSocketServer(){
    //socket.io不允许传递Localhost，开发环境会转化
    let url=window.location.origin;
    if (/localhost/.test(url)) {
        url=proxyConfig.env
    }
    return url;
}
export function parseSingleChat(list){
    const systemConfig = store.state.systemConfig
    let arr=[];
    list.forEach(item =>{
        if (item.type==systemConfig.ConversationConfig.type.Group) {
            arr.push(item)
        }        else if (item.type==systemConfig.ConversationConfig.type.Single) {
            const userInfo=item.attendeeList[0].userInfo;
            item.avatar=userInfo.avatar
            item.subject=userInfo.nickname
            item.role=userInfo.role
            item.sex=userInfo.sex
            item.fid=userInfo.id;
            arr.push(item)
        }
    })
    return arr;
}
export function getExamGroupSubject(exam) {
    let groupList = exam.group_subject
    let groupName = ''
    if(groupList && groupList.length > 0) {
        groupList.forEach((v,i) => {
            groupName += v.group_subject
            if(groupList[i+1]) {
                groupName += ','
            }
        })
    }else{
        groupName = window.$vm.$t('unknow_text')
    }
    return groupName
}
export function deDuplicatingImg(list){
    let obj={}
    let arr=[]
    for(let file of list){
        if (!obj[file.img_id]) {
            obj[file.img_id]=file
        }
    }
    for(let key in obj){
        arr.push(obj[key]);
    }
    return arr;
}
export function getLocalAvatar(oItem){      //20250528 type=1 是单聊，type=2 是群聊，type=3 是群组
    const systemConfig = store.state.systemConfig
    const item = {...oItem}
    let avatar_local='';
    if(item.is_single_chat===1||item.type===1||item.nickname){
        if (item.service_type==systemConfig.ServiceConfig.type.FileTransferAssistant) {
            avatar_local='static/resource_pc/images/transfer.png'
        }else{
            //单聊
            const userStatus=item.user_status||item.status
            if (userStatus===systemConfig.userStatus.Destroy) {
                avatar_local='static/resource_pc/images/destroy.png'
                //item.sex=2;todo 应该在别的地方实现
            }else if (!item.avatar||/user\/avatar\/default\/0\.png/.test(item.avatar)||/static\/resource_pc\/images\//.test(item.avatar)) {
                if(item.sex==1){
                    avatar_local='static/resource_pc/images/b3-1.png'
                }else{
                    avatar_local='static/resource_pc/images/b2-1.png'
                }
            }else{
                //给出默认头像
                avatar_local='';
            }
        }
    }else{
        if (item.type==3) {
            if (!item.avatar) {
                avatar_local='static/resource_pc/images/groupset.png'
            }
        }else if (!item.avatar) {
            avatar_local='static/resource_pc/images/b1.png'
        }
    }
    // const avatar=addRootToUrl(item.avatar);
    if (avatar_local==='') {
        avatar_local=addRootToUrl(item.avatar);
    }
    return avatar_local
}
export function desEncAes256(encData, password, iv){
    try {
        const decipher = crypto.createDecipheriv("aes-256-cbc", Buffer.from(password), Buffer.from(iv));
        let decrypted = decipher.update(encData, "base64");
        decrypted = Buffer.concat([decrypted, decipher.final()]);
        return decrypted.toString("utf8");
    } catch (e) {
        console.log(`解密错误${JSON.stringify({encData, password, iv})}`);
        throw new Error(`数据解密失败${e.message}`);
    }
}

export function parseServerInfo(json){
    let str=json.encStr
    const password = str.slice(-32); // 密码
    const iv = password.slice(0,16); // 向量
    console.log(password, iv);
    const data = JSON.parse(desEncAes256(str.slice(0, -32), password, iv));
    return data;
    //console.log(data); // 前端解密后的数据
}
export function getDefaultImg(obj){
    let url=''
    if(obj.type==3){
        url='static/resource_pc/images/groupset.png'
    }else if (obj.type==2||obj.is_single_chat===0) {
        url='static/resource_pc/images/b1.png'
    }else{
        if (obj.sex==1) {
            url='static/resource_pc/images/b3-1.png'
        }else{
            url='static/resource_pc/images/b2-1.png'
        }
    }
    // url=addRootToUrl(url);
    return url
}
export function getItemStatusText(status) {
    switch (status) {
    case 0:
        return i18n.t('case_exam_status.unsubmit')
    case 1:
        return i18n.t('case_exam_status.saved')
    case 2:
        return i18n.t('case_exam_status.submited')
    case 3:
        return i18n.t('case_exam_status.reject')
    case 4:
        return i18n.t('case_exam_status.assigned')
    case 5:
        return i18n.t('case_exam_status.reviewed')
    case 6:
        return i18n.t('case_exam_status.judgeSubmit')
    default:
        return i18n.t('unknow_text')
    }
}
export function getBaseUrl(){
    const systemConfig = store.state.systemConfig
    let ajaxServer=systemConfig.server_type.protocol+systemConfig.server_type.host+systemConfig.server_type.port;
    return ajaxServer;
}
export function getShowDes(name){
    if(name.length > 8){
        name = name.substring(0, 8) + '......'
    }
    return name;
}
export function getDateDiff(start, end){
    let startTime = moment(start).toDate().getTime();
    let endTime = moment(end).toDate().getTime();
    let diffTime = endTime - startTime;

    let diffDay = Math.floor(diffTime / (24 * 3600 * 1000)); //相差天数
    let lev1 = diffTime % (24 * 3600 * 1000);
    let diffHour = Math.floor(lev1 / (3600 * 1000)); //相差小时
    let lev2 = lev1 % (3600 * 1000);
    let diffMin = Math.floor(lev2 / (60 * 1000));
    let lev3 = lev2 % (60 * 1000);
    let diffSecond = String(Math.floor(lev3 / 1000));

    // let allMin = String(diffDay*24*60 + diffHour*60 + diffMin)
    let allHours = diffDay * 24 + diffHour;
    // let Str = `${allMin.length==1?'0'+allMin:allMin}:${diffSecond.length==1?'0'+diffSecond:diffSecond}`
    // let Str = `${allHours}${i18n.t('live_replay_hour')}${diffMin}${i18n.t('live_replay_minute')}${diffSecond}${i18n.t('live_replay_second')}`;
    let Str = `${diffSecond}${i18n.t('live_replay_second')}`
    if(diffMin>0){
        Str = `${diffMin}${i18n.t('live_replay_minute')}${Str}`
    }
    if(allHours>0){
        Str = `${allHours}${i18n.t('live_replay_hour')}${diffMin}${i18n.t('live_replay_minute')}`
    }
    return Str;
}
export function formatDurationTime(duration){
    let start_time = new Date().getTime() - (duration || 0)*1000
    let millisecond = new Date() - start_time
    let h = Math.floor(millisecond / (60 * 60 * 1000))
    h = h < 10 ? '0' + h : h
    let min = Math.floor((millisecond % (60 * 60 * 1000)) / (60 * 1000))
    min = min < 10 ? '0' + min : min
    let sec = Math.floor(((millisecond % (60 * 60 * 1000)) % (60 * 1000)) / 1000)
    sec = sec < 10 ? '0' + sec : sec
    let Str = `${sec}${i18n.t('live_replay_second')}`
    if(min>0){
        Str = `${min}${i18n.t('live_replay_minute')}${Str}`
    }
    if(h>0){
        Str = `${h}${i18n.t('live_replay_hour')}${min}${i18n.t('live_replay_minute')}`
    }
    return Str;
    // _this.count_time = h + ':' + min + ':' + sec
}
export function judgeIfCurrentYear(start, end){
    const currentYear = moment(start).toDate().getFullYear()
    const targetYear = moment(end).toDate().getFullYear()
    return currentYear === targetYear
}
export function getLiveRoomObj(oCid){
    const cid = oCid||window.vm.$route.params.cid
    let liveRoom = null
    try {
        if(Tool.checkAppClient('Browser')){
            liveRoom = window.CLiveRoomWeb&&window.CLiveRoomWeb[cid]
        }else{
            liveRoom = window.CLiveRoom&&window.CLiveRoom[cid]
        }
    } catch (error) {
        console.error('getLiveRoomObj',error)
        liveRoom = null
    }
    return liveRoom
}
export function EnforceSequentialExecution(funA,funB){
    new Promise((resolve,reject)=>{
        resolve();
    }).then(()=>{
        funA();
    }).then(()=>{
        funB();
    })
}
export function jumpRoute(level,path){
    window.directPath=path;
    window.vm.$router.go(-1*level);
}
export function getRecordSubject(message){
    if(message.live_record_data && message.live_record_data.subject){
        return message.live_record_data.subject
    }else if (message.live_record_data && message.live_record_data.creator_name) {
        return `${message.live_record_data.creator_name}${i18n.t('initiated_live_broadcast')}`;
    }else{
        return ''
    }
}
export function getClipSubject(message){
    return `${message.nickname}${i18n.t('generated_video_clips')}`
}
export function getResourceTempStatus(resource_id,key){
    const storeState = window.vm&&window.vm.$store&&window.vm.$store.state
    if(!storeState){
        return
    }
    const resourceTempStatus = storeState.resourceTempStatus;
    if (resourceTempStatus.hasOwnProperty(resource_id)) {
        if (resourceTempStatus[resource_id].hasOwnProperty(key)) {
            return resourceTempStatus[resource_id][key]
        }else{
            return null
        }
    }else{
        return null
    }
}
export function checkResourceType(currentFile){
    const systemConfig = store.state.systemConfig
    if(currentFile.msg_type==systemConfig.msg_type.Image||
    currentFile.img_type_ex==systemConfig.msg_type.Image||
    currentFile.img_type_ex==systemConfig.msg_type.Frame||
    currentFile.msg_type==systemConfig.msg_type.Frame   ||
    currentFile.img_type_ex==systemConfig.msg_type.OBAI ||
    currentFile.msg_type==systemConfig.msg_type.OBAI
    ){
        return 'image'
    }else if(currentFile.msg_type==systemConfig.msg_type.Cine||
    currentFile.img_type_ex==systemConfig.msg_type.Cine||
    currentFile.msg_type==systemConfig.msg_type.Video||
    currentFile.img_type_ex==systemConfig.msg_type.Video){
        return 'video'
    }else if(currentFile.msg_type==systemConfig.msg_type.RealTimeVideoReview||
    currentFile.img_type_ex==systemConfig.msg_type.RealTimeVideoReview||
    currentFile.img_type_ex==systemConfig.msg_type.VIDEO_CLIP||
    currentFile.msg_type==systemConfig.msg_type.VIDEO_CLIP){
        return 'review_video'
    }
}
export function resetGlobalCustomWindowObject(){
    window.main_screen=undefined
    window.CLiveRoomWeb=undefined
    window.CLiveRoom=undefined
    window.agoraClient={}
    window.CReverseControl={}
    window.livingStatus = 0
}
export function checkIsCreator(cid){
    const user = store.state.user
    const conversation = store.state.conversationList[cid];
    return conversation&&(user.uid==conversation.creator_id)&&(conversation.is_single_chat==0)
}
export function checkIsManager(cid){
    const systemConfig = store.state.systemConfig
    const user = store.state.user
    const conversation = store.state.conversationList[cid];
    let isManager = false;
    if (conversation) {
        for(let key in conversation.attendeeList){
            let item = conversation.attendeeList[key];
            if (item.role == systemConfig.groupRole.manager && item.userid == user.uid) {
                isManager = true;
                break;
            }
        }
    }
    return isManager;
}
export function checkIsManagerOrCreator(cid) {
    const systemConfig = store.state.systemConfig
    const user = store.state.user
    const conversation = store.state.conversationList[cid];
    let isManagerOrCreator = false;
    if (conversation) {
        for(let key in conversation.attendeeList){
            let item = conversation.attendeeList[key];
            if ([systemConfig.groupRole.manager, systemConfig.groupRole.creator].includes(item.role) && item.userid == user.uid) {
                isManagerOrCreator = true;
                break;
            }
        }
    }
    return isManagerOrCreator;
}
export function destroyAllConference(){
    window.CWorkstationCommunicationMng.hideRealTimeVideo({})
    window.CWorkstationCommunicationMng.CloseVideoWall()
    if(window.main_screen&&window.main_screen.CMonitorWallPush&&window.main_screen.CMonitorWallPush.joined){//电视墙推流退出房间逻辑
        window.main_screen.CMonitorWallPush.LeaveChannelSilence()
    }
    // if(window.agoraClient){ //电视墙的退出房间逻辑
    //     Object.keys(window.agoraClient).forEach(key=>{
    //         window.agoraClient[key].event.offAll()
    //         window.agoraClient[key].LeaveChannelMonitor()
    //         delete window.agoraClient[key]
    //         console.log(`离开房间成功----->${key}`);
    //     })
    // }
    if(window.vm.$root.currentLiveCid&&window.main_screen.conversation_list[window.vm.$root.currentLiveCid]){
        let liveRoom = getLiveRoomObj()
        liveRoom.LeaveChannelAux('normal')
    }


}
export function getMoreResourceList(resource_id,cid){
    const systemConfig = store.state.systemConfig
    const msg_type = systemConfig.msg_type
    const unLoadResourceTypeArr = [
        msg_type.Sound,
        msg_type.WITHDRAW,
        msg_type.EXAM_IMAGES,
        msg_type.File,
        msg_type.AI_ANALYZE,
        msg_type.IWORKS_PROTOCOL
    ];
    const data = []
    return new Promise((resolve,reject)=>{
        window.main_screen.conversation_list[cid].getResourceList(
            {
                limit: systemConfig.consultationImageShowNum,
                type:'all',
                lastResourceID: resource_id,
            },
            (res) => {
                if (res.error_code === 0) {
                    res.data.forEach(item=>{
                        if(!unLoadResourceTypeArr.includes(item.msg_type)){
                            data.push(item)
                        }
                    })
                    resolve(data)
                } else {
                    resolve([])
                    console.error('getResourceList error 1')
                    // Toast("getResourceList error 1");
                }
            }
        );

    })
}
export function getMessageAiReportFromLocal(msg){
    let item = msg//JSON.parse(JSON.stringify(msg))
    if(!item||!item.resource_id){
        return null;
    }
    let  ai_analyze_report= null
    let storeItem = store.state.gallery.commentObj[item.resource_id]//JSON.parse(JSON.stringify(store.state.gallery.commentObj[item.resource_id]));
    const get_report= (item,key)=>{
        let temp = null
        if(storeItem&&storeItem.ai_analyze_report && storeItem.ai_analyze_report&&storeItem.ai_analyze_report[key]&&storeItem.ai_analyze_report[key][item.resource_id]){
            temp = storeItem.ai_analyze_report
        }
        if(item.ai_analyze_report&& item.ai_analyze_report[key]&&item.ai_analyze_report[key][item.resource_id]){
            temp = item.ai_analyze_report
        }
        if(item.ai_analyze_report&& item.ai_analyze_report.report&& item.ai_analyze_report.report[key]&&item.ai_analyze_report.report[key][item.resource_id]){
            temp = {...item.ai_analyze_report, ...item.ai_analyze_report.report}
            delete temp.report
        }
        if(item.ai_analyze&&item.ai_analyze.status&&item.ai_analyze.report&&item.ai_analyze.report[key]&&item.ai_analyze.report[key][item.resource_id]){
            let ai_analyze = item.ai_analyze
            temp = {
                id:ai_analyze.ai_analyze_id,
                group_id:ai_analyze.group_id,
                sender_id:ai_analyze.sender_id,
                send_ts:ai_analyze.send_ts,
                type:ai_analyze.type,
                status:ai_analyze.status,
                error:ai_analyze.report.error,
            }
            temp = {...item.ai_analyze.report, ...temp}
        }
        return temp
    }
    ai_analyze_report = get_report(item, 'clips')
    if(!ai_analyze_report){
        ai_analyze_report = get_report(item,'mark_list')
    }
    return cloneDeep(ai_analyze_report);
}

export function imageStandardIcon(msg){
    let item = cloneDeep(msg)
    const functionsStatus = store.state.globalParams.functionsStatus;
    const systemConfig = store.state.systemConfig
    // const cid = item.group_id||item.groupid;
    // const convsrsation = store.state.conversationList[cid]
    let ai_analyze_report =getMessageAiReportFromLocal(item)
    let ai_analyze_type = ai_analyze_report?.type
    let result = {css:'',type:'',label:'',tips:''}
    if(functionsStatus.drAIAssistant && ai_analyze_type == store.state.aiPresetData.typeIndex.drChest){
        result = {css:'dr_result_icon',type:'DrAiAnalyze',label:'',tips:''}
        if(ai_analyze_report&&ai_analyze_report.ai_analyze_id){
            if(ai_analyze_report.error){
                return [result]
            }else{
                let clips=ai_analyze_report.clips
                if(clips&&clips[item.resource_id]){
                    let standard_desc = window.vm.$store.state.aiPresetData.DrViews.standard_desc
                    if('score' in clips[item.resource_id] ){
                        result.css = result.css + ' bg_blue'
                        result.label = i18n.t('level_b')

                        if(clips[item.resource_id].score>standard_desc.ai_height.lowest){
                            result.css = result.css + ' bg_blue'
                            result.label = i18n.t('level_a')
                        }
                        if(clips[item.resource_id].score<=standard_desc.ai_lower.highest){
                            result.css = result.css + ' bg_red'
                            result.label = i18n.t('level_c')
                        }
                    }
                }
            }
        }
        return [result]
    }
    ///腹部
    if( functionsStatus.breastAI && ai_analyze_type&& (item.msg_type == systemConfig.msg_type.AI_ANALYZE||item.ai_analyze_id||ai_analyze_report)
    ){
        // if(ai_analyze_type == store.state.aiPresetData.typeIndex.abdomen||ai_analyze_type == store.state.aiPresetData.typeIndex.cardiac){
        if(ai_analyze_type == store.state.aiPresetData.typeIndex.abdomen){
            result = {css:'icon iconfont',type:'AiAnalyzeAbdomenCip',label:'',tips:''}
            if(ai_analyze_report&&ai_analyze_report.clips){
                if(ai_analyze_report.error){
                    if(!item.url){
                        result.css = result.css + ' ai_result_deletion_icon iconwenhao-yuankuang-copy'
                        result.tips = i18n.t('view_deletion')
                    }else{
                        // result.css = result.css + ' ai_result_deletion_icon iconwenhao-yuankuang-copy'
                        result.tips = i18n.t('no_ai_result')
                    }
                }
                let clips=ai_analyze_report.clips
                if(clips&&clips[item.resource_id]){
                    if('quality' in clips[item.resource_id][0] && clips[item.resource_id][0].quality<2){
                        result.css = result.css + '  qc_standard_icon iconduihao-yuankuang'
                        if(clips[item.resource_id][0].quality<1){
                            result.tips = i18n.t('view_quality_text') + i18n.t('standard')
                        }else{
                            result.tips = i18n.t('view_quality_text') + i18n.t('basic_standard')
                        }
                    }else{
                        result.css = result.css + '  qc_non_standard_icon icongantanhao-yuankuang'
                        result.tips = i18n.t('view_quality_text') + i18n.t('non_standard')
                    }
                    if(clips[item.resource_id][0] && clips[item.resource_id][0].clip_id && clips[item.resource_id][0].clip_id.toLowerCase()=='undefined'){
                        result = {css:'icon iconfont',type:'AiAnalyzeAbdomenCip',label:'',tips:''}
                    }
                }else{
                    if(!item.url){
                        result.css = result.css + ' ai_result_deletion_icon iconwenhao-yuankuang-copy'
                        result.tips = i18n.t('view_deletion')
                    }
                }
            }else{
                if(!item.url){
                    result.css = result.css + ' ai_result_deletion_icon iconwenhao-yuankuang-copy'
                    result.tips = i18n.t('view_deletion')
                }
            }
            return [result]
        }
        if(ai_analyze_type == store.state.aiPresetData.typeIndex.obstetrical){
            result = {css:'',type:'AiAnalyzeObstetrical',label:'',tips:''}
            if(ai_analyze_report){
                let clips=ai_analyze_report.clips||{}
                const mc_resource_map = clips[item.resource_id]? clips[item.resource_id][0] : {}
                if( mc_resource_map
                && mc_resource_map.finshed
                && mc_resource_map.report
                ){
                    let ai_result = mc_resource_map.report
                    if(!ai_result.isSuccess){
                        result.css = result.css + 'error'
                    }
                    if(ai_result && 'view_quality' in ai_result && ai_result.view_type!=null&&ai_result.view_quality!=null){
                        if(ai_result.view_quality<2){
                            result.css = result.css + 'icon iconfont qc_standard_icon iconduihao-yuankuang'
                            if(ai_result.view_quality<1){
                                result.tips = i18n.t('view_quality_text') + i18n.t('standard')
                            }else{
                                result.tips = i18n.t('view_quality_text') + i18n.t('basic_standard')
                            }
                        }else{
                            result.css = result.css + 'icon iconfont qc_non_standard_icon icongantanhao-yuankuang'
                            result.tips = i18n.t('view_quality_text') + i18n.t('non_standard')
                        }
                    }else{
                        result.css = result.css + 'error'
                    }
                }else{
                    result.css = result.css + 'error'
                }
            }else{
                result.css = result.css + 'error'
            }
            return [result]
        }
    }
    if(item&&item.mc_resource_map){
        result = {css:'',type:'AiAnalyzeObstetrical',label:'',tips:''}
        if( functionsStatus.obstetricalAI
            && item.mc_resource_map
            && item.mc_resource_map.ai_report
            && item.mc_resource_map.ai_report.finshed
            && item.mc_resource_map.ai_report.report
        ){
            let ai_result = item.mc_resource_map.ai_report.report
            if(!ai_result.isSuccess){
                result.css = result.css + 'error'
            }
            if(ai_result && 'view_quality' in ai_result&& item.mc_resource_map.type!=null&&item.mc_resource_map.quality!=null){
                if(ai_result.view_quality<2){
                    result.css = result.css + 'icon iconfont qc_standard_icon iconduihao-yuankuang'
                    if(ai_result.view_quality<1){
                        result.tips = i18n.t('view_quality_text') + i18n.t('standard')
                    }else{
                        result.tips = i18n.t('view_quality_text') + i18n.t('basic_standard')
                    }
                }else{
                    result.css = result.css + 'icon iconfont qc_non_standard_icon icongantanhao-yuankuang'
                    result.tips = i18n.t('view_quality_text') + i18n.t('non_standard')
                }
            }else{
                result.css = result.css + 'error'
            }
        }else{
            result.css = result.css + 'error'
        }
        return [result]
    }
    return [result]
}


export function toFixedNumber(q, m=1){
    if(!q || q==undefined || q==null){
        return 0
    }
    q=parseFloat(q)
    if(q<100&&q>0){
        return parseFloat(q.toFixed(m))
    }else{
        return parseFloat(q.toFixed(0))
    }
}
export function parseObjToArr(data){
    var arr=[]
    for(let item in data){
        arr.push({...data[item],id:item})
    }
    return arr
}
export function formatAttendeeNickname(attendeeList){
    let list = parseObjToArr(attendeeList);
    const remarkMap = window.vm.$store.state.friendList.remarkMap;
    let filterList = []; //后端把所有用户都返回回来，前端只显示未退群用户
    for (let i = 0; i < list.length; i++) {
        list[i].remarkMapNickname = remarkMap [list[i].userid];
        if (list[i].attendeeState != 0) {
            filterList.push(list[i]);
        }
        list[i].showNickname = list[i].alias_name || list[i].remarkMapNickname || list[i].nickname
    }
    return filterList;
}
export function formatAttendeeNicknameToMap(attendeeList){
    let list = parseObjToArr(attendeeList);
    const remarkMap = window.vm.$store.state.friendList.remarkMap;
    let filterObj = {}; // 使用对象存储数据，以 uid 为键，showNickname 为值
    for (let i = 0; i < list.length; i++) {
        list[i].remarkMapNickname = remarkMap[list[i].userid];
        if (list[i].attendeeState != 0) {
            filterObj[list[i].userid] = list[i].alias_name || list[i].remarkMapNickname || list[i].nickname;
        }
    }
    return filterObj;
}
//Dr数据转换
export function getBodyMarkNameByKey(key){
    if(key){
        switch (key) {
        case "P04":
            return '胸部';
        case "P04_0005":
            return '胸部';
        case "V04_0014":
            return '胸部后前位';
        case "V04_0015":
            return '胸部前后位';
        default:
            return key
        }
    }else{
        return key
    }
}
export function getResourceTempState(resource_id) { //获取资源的状态
    const resourceTempStatus = window.vm.$store.state.resourceTempStatus;
    if (resourceTempStatus.hasOwnProperty(resource_id)) {
        if (resourceTempStatus[resource_id].hasOwnProperty("state")) {
            return resourceTempStatus[resource_id].state;
        } else {
            return 1;
        }
    } else {
        return 1;
    }
}
export async function galleryAlert(msg) { //画廊内弹提示
    window.CWorkstationCommunicationMng.hideRealTimeVideo({})
    const confirmResult=await window.vm.$alert(msg)
    window.CWorkstationCommunicationMng.showRealTimeVideo({type:4})
}
export  function showProtocolName(exam,imagge) { //画廊内弹提示
    if(exam.protocol_execution_guid||exam.protocol_execution_guid){
        let ai_analyze_report =getMessageAiReportFromLocal(imagge)
    }
    return ''
}
export  function setClamped(item, length = 7) {
    const subject = item.subject;
    if (subject.length > length) {
        //群名大于length个字符需要判断是否截断
        const chinaNum = subject.match(/[^\x00-\xff]/g);
        const chinaLength = chinaNum ? chinaNum.length : -1; //无汉字可以多1个字符空间
        if (chinaLength + subject.length > length * 2) {
            window.vm.$set(item, "clamped", true);
        }
    }
}

export function checkPrivacyPolicy(){ // 检测是否同意隐私策略
    const systemConfig = store.state.systemConfig
    let serverType = localStorage.getItem('serverType') || '云++'
    let privacyStatus = JSON.parse(localStorage.getItem('isAgreePrivacyPolicy')||"{}")

    // 从envConfig中获取版本号，如果没有则使用默认值
    let privacy_version = systemConfig.envConfig && systemConfig.envConfig.privacy_agreement_version
    // 获取用户已同意的版本号
    let agreedVersion = privacyStatus[serverType]

    // 是否同意过隐私协议
    let hasAgreed = !!(agreedVersion && agreedVersion !== 0)

    // 是否需要再次同意隐私协议
    let needReagree = false
    if (hasAgreed) {
        // 只要版本号不一致，就强制认为需要再次同意隐私协议
        needReagree =privacy_version && (String(agreedVersion) !== String(privacy_version))
    }

    return {
        hasAgreed: hasAgreed,        // 是否同意过
        needReagree: needReagree     // 是否需要再次同意
    }
}

export function checkPrivacyPolicyStrict(){ // 严格检测隐私策略（需要版本号匹配）
    const privacyCheck = checkPrivacyPolicy()
    // 严格模式：必须同意过且不需要重新同意
    return privacyCheck.hasAgreed && !privacyCheck.needReagree
}
export function goPrivacyPolicy(){
    const server_type = window.vm.$store.state.systemConfig.server_type;
    // 根据CN/CE环境判断隐私协议语言，而不是根据当前语言设置
    // CN环境使用中文隐私协议，CE环境使用英文隐私协议
    const evn = process.env.VUE_APP_PROJECT_NOV || 'CN';
    let privacy_version = window.vm.$store.state.systemConfig.envConfig && window.vm.$store.state.systemConfig.envConfig.privacy_agreement_version
    let host = server_type.protocol + server_type.host + server_type.port;
    if (host.indexOf("localhost") > -1 || host.indexOf("192.168") > -1) {
        host = `https://${Tool.getHostConfig().dev}`;
    }
    const url = host + `/privacyPolicyPage/${evn}/pravicyPolicy${evn}_${privacy_version}.html`;
    if (Tool.checkAppClient('Browser')) {
        window.open(url, "blank");
    } else {
        window.CWorkstationCommunicationMng.OpenNewWindow({ url });
    }
}
