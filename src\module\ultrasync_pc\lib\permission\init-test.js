/**
 * 权限管理器初始化测试
 * 用于验证权限管理器是否正确初始化
 */

import permissionManager from './index.js';

/**
 * 测试权限管理器初始化状态
 */
export function testPermissionManagerInit() {
    console.log('=== 权限管理器初始化测试 ===');
    
    // 检查初始化状态
    console.log('初始化状态:', permissionManager.isInitialized());
    
    // 检查用户信息
    console.log('用户信息:', permissionManager.getUserInfo());
    
    // 测试权限检查（未初始化时应该返回false）
    console.log('backgroundManage权限:', permissionManager.checkPermission('backgroundManage'));
    console.log('userManage权限:', permissionManager.checkPermission('userManage'));
    console.log('isAdmin:', permissionManager.isAdmin());
    console.log('isSuperAdmin:', permissionManager.isSuperAdmin());
    
    console.log('=== 测试完成 ===');
}

/**
 * 模拟用户登录并测试权限初始化
 */
export function simulateUserLogin() {
    console.log('=== 模拟用户登录测试 ===');
    
    // 模拟不同角色的用户
    const testUsers = [
        { uid: 'test1', username: 'user1', role: 1, nickname: '普通用户' },
        { uid: 'test2', username: 'admin1', role: 3, nickname: '管理员' },
        { uid: 'test3', username: 'superadmin1', role: 5, nickname: '超级管理员' }
    ];
    
    testUsers.forEach((user, index) => {
        console.log(`\n--- 测试用户 ${index + 1}: ${user.nickname} ---`);
        
        // 初始化权限管理器
        permissionManager.initialize(user).then(() => {
            console.log('初始化成功');
            console.log('初始化状态:', permissionManager.isInitialized());
            console.log('用户角色:', permissionManager.getUserRole());
            console.log('isAdmin:', permissionManager.isAdmin());
            console.log('isSuperAdmin:', permissionManager.isSuperAdmin());
            console.log('backgroundManage权限:', permissionManager.checkPermission('backgroundManage'));
            console.log('userManage权限:', permissionManager.checkPermission('userManage'));
            console.log('user_delete权限:', permissionManager.checkPermission('user_delete'));
        }).catch(error => {
            console.error('初始化失败:', error);
        });
    });
}

/**
 * 监听权限管理器事件
 */
export function listenPermissionEvents() {
    console.log('=== 监听权限管理器事件 ===');
    
    // 监听初始化完成事件
    window.addEventListener('permission:initialized', (event) => {
        console.log('权限管理器初始化完成事件:', event.detail);
    });
    
    // 监听用户信息更新事件
    window.addEventListener('permission:userInfoUpdated', (event) => {
        console.log('用户信息更新事件:', event.detail);
    });
}

/**
 * 检查Vue实例中的权限方法
 */
export function testVuePermissionMethods() {
    console.log('=== 测试Vue权限方法 ===');
    
    if (window.vm) {
        console.log('Vue实例存在');
        
        // 测试混入方法
        if (typeof window.vm.$checkPermission === 'function') {
            console.log('$checkPermission方法存在');
            console.log('backgroundManage权限:', window.vm.$checkPermission('backgroundManage'));
        } else {
            console.log('$checkPermission方法不存在');
        }
        
        if (typeof window.vm.$isAdmin === 'function') {
            console.log('$isAdmin方法存在');
            console.log('isAdmin:', window.vm.$isAdmin());
        } else {
            console.log('$isAdmin方法不存在');
        }
        
        if (typeof window.vm.$isSuperAdmin === 'function') {
            console.log('$isSuperAdmin方法存在');
            console.log('isSuperAdmin:', window.vm.$isSuperAdmin());
        } else {
            console.log('$isSuperAdmin方法不存在');
        }
    } else {
        console.log('Vue实例不存在');
    }
}

/**
 * 运行所有测试
 */
export function runAllInitTests() {
    console.log('🚀 开始运行权限管理器初始化测试...\n');
    
    // 监听事件
    listenPermissionEvents();
    
    // 测试初始状态
    testPermissionManagerInit();
    
    // 测试Vue方法
    testVuePermissionMethods();
    
    // 模拟用户登录
    setTimeout(() => {
        simulateUserLogin();
    }, 1000);
    
    console.log('\n✅ 所有初始化测试已启动!');
}

// 如果在浏览器环境中，添加到全局对象
if (typeof window !== 'undefined') {
    window.permissionInitTest = {
        runAllInitTests,
        testPermissionManagerInit,
        simulateUserLogin,
        testVuePermissionMethods,
        listenPermissionEvents
    };
    
    console.log('权限管理器初始化测试工具已加载到 window.permissionInitTest');
    console.log('运行 window.permissionInitTest.runAllInitTests() 开始测试');
}

/**
 * 检查store中的用户信息
 */
export function checkStoreUserInfo() {
    console.log('=== 检查Store用户信息 ===');
    
    if (window.vm && window.vm.$store) {
        const user = window.vm.$store.state.user;
        console.log('Store中的用户信息:', user);
        
        if (user && user.uid) {
            console.log('用户已登录，UID:', user.uid);
            console.log('用户角色:', user.role);
            console.log('用户昵称:', user.nickname);
        } else {
            console.log('用户未登录或信息不完整');
        }
    } else {
        console.log('Vue实例或Store不存在');
    }
}

/**
 * 手动触发权限管理器初始化
 */
export function manualInitPermissionManager() {
    console.log('=== 手动初始化权限管理器 ===');
    
    if (window.vm && window.vm.$store) {
        const user = window.vm.$store.state.user;
        
        if (user && user.uid) {
            console.log('使用Store中的用户信息初始化权限管理器:', user);
            
            permissionManager.initialize(user).then(() => {
                console.log('手动初始化成功');
                console.log('初始化状态:', permissionManager.isInitialized());
                console.log('用户角色:', permissionManager.getUserRole());
                console.log('backgroundManage权限:', permissionManager.checkPermission('backgroundManage'));
            }).catch(error => {
                console.error('手动初始化失败:', error);
            });
        } else {
            console.log('Store中没有有效的用户信息');
        }
    } else {
        console.log('Vue实例或Store不存在');
    }
}

// 添加手动初始化方法到全局对象
if (typeof window !== 'undefined') {
    window.permissionInitTest.checkStoreUserInfo = checkStoreUserInfo;
    window.permissionInitTest.manualInitPermissionManager = manualInitPermissionManager;
}
