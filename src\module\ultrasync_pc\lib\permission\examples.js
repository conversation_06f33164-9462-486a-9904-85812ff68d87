/**
 * 权限管理器使用示例
 * 展示如何在现有项目中集成和使用权限管理器
 */

import permissionManager from './index.js';
import { initializePermissionManager, integrateWithRouter, integrateWithVue } from './integration.js';

// ===== 1. 在main.js中初始化 =====
export function initInMainJs() {
    // 方式1: 手动初始化
    const userInfo = {
        uid: '123',
        role: 2,
        type: 1,
        nickname: '管理员'
    };

    permissionManager.initialize(userInfo).then(() => {
        console.log('权限管理器初始化成功');
    });

    // 方式2: 自动从store初始化
    initializePermissionManager().then(() => {
        console.log('权限管理器自动初始化成功');
    });
}

// ===== 2. 在路由中使用 =====
export function routerExample(router) {
    // 集成到现有路由守卫
    integrateWithRouter(router);

    // 或者手动添加权限检查
    router.beforeEach((to, from, next) => {
        if (permissionManager.checkRoutePermission(to.path)) {
            next();
        } else {
            next('/login');
        }
    });
}

// ===== 3. 在Vue组件中使用 =====
export const ComponentExample = {
    template: `
        <div>
            <!-- 使用指令控制显示 -->
            <button v-permission="'user_create'">创建用户</button>
            <button v-permission.hide="'user_delete'">删除用户</button>
            <button v-permission.disable="'user_edit'">编辑用户</button>

            <!-- 使用计算属性控制 -->
            <div v-if="canManageUsers">
                <h3>用户管理</h3>
                <button @click="createUser" :disabled="!canCreateUser">创建用户</button>
                <button @click="deleteUser" v-show="canDeleteUser">删除用户</button>
            </div>

            <!-- 管理员专用功能 -->
            <div v-if="isAdmin">
                <h3>管理员面板</h3>
                <router-link to="/admin" v-if="canAccessAdmin">后台管理</router-link>
            </div>
        </div>
    `,

    computed: {
        // 检查用户管理权限
        canManageUsers() {
            return this.$checkFeature('user_management');
        },

        canCreateUser() {
            return this.$checkFeature('user_management', 'create_user');
        },

        canDeleteUser() {
            return this.$checkFeature('user_management', 'delete_user');
        },

        // 检查是否为管理员
        isAdmin() {
            return this.$isAdmin();
        },

        // 检查管理员路由权限
        canAccessAdmin() {
            return this.$checkRoute('/admin');
        }
    },

    methods: {
        createUser() {
            if (this.canCreateUser) {
                // 执行创建用户逻辑
                console.log('创建用户');
            } else {
                this.$message.error('权限不足');
            }
        },

        deleteUser() {
            if (this.canDeleteUser) {
                // 执行删除用户逻辑
                console.log('删除用户');
            } else {
                this.$message.error('权限不足');
            }
        }
    }
};

// ===== 4. 改造现有的leftSideBar组件 =====
export const LeftSideBarExample = {
    template: `
        <div class="sidebar">
            <ul class="menu">
                <li v-for="item in filteredMenuItems" :key="item.name">
                    <span @click="handleMenuClick(item)">{{ item.label }}</span>
                </li>
            </ul>
        </div>
    `,

    data() {
        return {
            menuItems: [
                {
                    name: 'system_setting',
                    label: '设置',
                    icon: 'el-icon-setting',
                    action: 'openSystemSetting'
                },
                {
                    name: 'system_info',
                    label: '系统信息',
                    icon: 'el-icon-info',
                    action: 'openVersion'
                },
                {
                    name: 'background_manage',
                    label: '后台管理',
                    icon: 'el-icon-s-tools',
                    action: 'openBackgroundManage'
                }
            ]
        };
    },

    computed: {
        // 使用权限管理器过滤菜单项
        filteredMenuItems() {
            return this.menuItems.filter(item => {
                return this.$checkComponent('menu', item.name);
            });
        }
    },

    methods: {
        handleMenuClick(item) {
            // 再次检查权限
            if (this.$checkComponent('menu', item.name)) {
                this[item.action]();
            } else {
                this.$message.error('权限不足');
            }
        },

        openSystemSetting() {
            console.log('打开系统设置');
        },

        openVersion() {
            console.log('打开版本信息');
        },

        openBackgroundManage() {
            console.log('打开后台管理');
        }
    }
};

// ===== 5. 多中心权限控制示例 =====
export const MulticenterExample = {
    template: `
        <div class="multicenter">
            <div v-if="canAccessMulticenter">
                <!-- 根据角色显示不同视图 -->
                <admin-view v-if="isMulticenterAdmin" />
                <judge-view v-if="isMulticenterJudge" />
                <review-view v-if="isMulticenterReviewer" />
                <normal-view v-else />

                <!-- 操作按钮 -->
                <div class="actions">
                    <button v-if="canCreateExam" @click="createExam">创建检查</button>
                    <button v-if="canAssignExam" @click="assignExam">分配检查</button>
                    <button v-if="canReviewExam" @click="reviewExam">审核检查</button>
                    <button v-if="canJudgeExam" @click="judgeExam">仲裁检查</button>
                </div>
            </div>
            <div v-else>
                <p>您没有权限访问多中心功能</p>
            </div>
        </div>
    `,

    computed: {
        canAccessMulticenter() {
            return this.$checkFeature('multicenter');
        },

        isMulticenterAdmin() {
            return this.$checkComponent('multicenter', 'admin_view');
        },

        isMulticenterJudge() {
            return this.$checkComponent('multicenter', 'judge_view');
        },

        isMulticenterReviewer() {
            return this.$checkComponent('multicenter', 'annotation_view');
        },

        canCreateExam() {
            return this.$checkFeature('multicenter', 'create_exam');
        },

        canAssignExam() {
            return this.$checkFeature('multicenter', 'assign_exam');
        },

        canReviewExam() {
            return this.$checkFeature('multicenter', 'review_exam');
        },

        canJudgeExam() {
            return this.$checkFeature('multicenter', 'judge_exam');
        }
    },

    methods: {
        createExam() {
            console.log('创建检查');
        },

        assignExam() {
            console.log('分配检查');
        },

        reviewExam() {
            console.log('审核检查');
        },

        judgeExam() {
            console.log('仲裁检查');
        }
    }
};

// ===== 6. API调用权限控制示例 =====
export class ApiService {
    static async createUser(userData) {
        // 检查API权限
        if (!permissionManager.checkApiPermission('POST', '/api/user/create')) {
            throw new Error('没有权限调用此API');
        }

        // 执行API调用
        return fetch('/api/user/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${permissionManager.getUserInfo().token}`
            },
            body: JSON.stringify(userData)
        });
    }

    static async deleteUser(userId) {
        if (!permissionManager.checkApiPermission('DELETE', '/api/user/delete')) {
            throw new Error('没有权限删除用户');
        }

        return fetch(`/api/user/delete/${userId}`, {
            method: 'DELETE',
            headers: {
                'Authorization': `Bearer ${permissionManager.getUserInfo().token}`
            }
        });
    }

    static async getUserList(params = {}) {
        // 检查数据权限
        let scope = 'own_data';
        if (permissionManager.checkDataPermission('user_data', 'all_data')) {
            scope = 'all_data';
        } else if (permissionManager.checkDataPermission('user_data', 'department_data')) {
            scope = 'department_data';
        }

        return fetch(`/api/user/list?scope=${scope}`, {
            headers: {
                'Authorization': `Bearer ${permissionManager.getUserInfo().token}`
            }
        });
    }
}

// ===== 7. 批量权限检查示例 =====
export function batchPermissionExample() {
    const permissions = permissionManager.batchCheckPermissions({
        routes: [
            { route: '/admin', key: 'admin_page' },
            { route: '/users', key: 'user_page' },
            { route: '/groups', key: 'group_page' }
        ],
        components: [
            { component: 'menu', action: 'background_manage', key: 'admin_menu' },
            { component: 'button', action: 'delete', key: 'delete_button' },
            { component: 'table', action: 'user_role_edit', key: 'role_edit' }
        ],
        features: [
            { feature: 'user_management', action: 'create_user', key: 'create_user' },
            { feature: 'group_management', action: 'edit_group', key: 'edit_group' },
            { feature: 'multicenter', action: 'assign_exam', key: 'assign_exam' }
        ]
    });

    console.log('批量权限检查结果:', permissions);

    // 根据权限结果配置界面
    return {
        showAdminPage: permissions.admin_page,
        showUserPage: permissions.user_page,
        showGroupPage: permissions.group_page,
        showAdminMenu: permissions.admin_menu,
        enableDeleteButton: permissions.delete_button,
        enableRoleEdit: permissions.role_edit,
        canCreateUser: permissions.create_user,
        canEditGroup: permissions.edit_group,
        canAssignExam: permissions.assign_exam
    };
}

// ===== 8. 用户信息更新示例 =====
export function userInfoUpdateExample() {
    // 监听store中用户信息变化
    if (window.vm && window.vm.$store) {
        window.vm.$store.watch(
            state => state.user,
            (newUser, oldUser) => {
                if (newUser && newUser.uid && permissionManager.isInitialized()) {
                    console.log('用户信息已更新，刷新权限');
                    permissionManager.updateUserInfo(newUser);
                }
            },
            { deep: true }
        );
    }

    // 手动更新用户信息
    const newUserInfo = {
        uid: '123',
        role: 3, // 角色升级
        type: 1,
        nickname: '高级管理员'
    };

    permissionManager.updateUserInfo(newUserInfo);
}
